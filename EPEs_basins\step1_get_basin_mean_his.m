clc;clear;close all

addpath '/mnt/sdb2/yuanshuai/code'

geo_filename ='/mnt/sdb2/yuanshuai/wanpan/yuan/clip_biaozhun_tif/clip_tif_global_600_1440.tif';
mask_geo_filename ='/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new02/shp/valid_pixels/updated_valid_pixels_basin_id.tif';

[a,R]=geotiffread(geo_filename);%先导入投影信息
info=geotiffinfo(geo_filename);%同上
[m,n]=size(a);

% 读入掩膜文件
[mask_data,~] = geotiffread(mask_geo_filename);
mask_data = reshape(mask_data,m*n,1);
% 获取所有唯一的流域ID(排除0和NaN)
basin_ids = unique(mask_data);
basin_ids = basin_ids(basin_ids > 0);

for y = 1971:2020
    y_str = num2str(y);


    % 读取mat文件
    mat_path = sprintf('/mnt/sdb2/yuanshuai/wanpan/yuan/ERA5-Land/fbl_025/mat/Pre/day/Pre_%d.mat', y);
    mat_data = load(mat_path);
    data_all = mat_data.Pre_year;
       
    % 计算每个流域的逐日平均值
    basin_daily_means = zeros(length(basin_ids), 365);
    for b = 1:length(basin_ids)
        basin_id = basin_ids(b);
        % 获取当前流域的所有网格点
        basin_pixels = mask_data == basin_id;
        % 计算该流域每天的均值
        for day = 1:365
            basin_daily_means(b, day) = mean(data_all(basin_pixels, day), 'omitnan');
        end
    end
    
    % 准备CSV数据
    % 创建表头
    headers = {'BasinID'};
    for day = 1:365
        headers = [headers, {sprintf('Day_%03d', day)}];
    end
    
    % 创建数据矩阵
    csv_data = zeros(length(basin_ids), 366);  % 366列：1列BasinID + 365列数据
    csv_data(:,1) = basin_ids;  % 第一列是流域ID
    csv_data(:,2:end) = basin_daily_means;  % 其余列是每日数据
    
    % 转换为表格
    T = array2table(csv_data, 'VariableNames', headers);
    
    % 保存为CSV文件
    output_dir = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03//pre_data/historical/basins_daily_mean';
    if ~exist(output_dir, 'dir')
        mkdir(output_dir);
    end
    csv_filename = fullfile(output_dir, sprintf('basin_daily_means_%s.csv', y_str));
    writetable(T, csv_filename);
    fprintf('已完成%d年数据处理\n', y);
end
