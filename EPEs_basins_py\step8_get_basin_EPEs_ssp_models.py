import os
import numpy as np
import pandas as pd

def compute_precip_indices(data_all, pre_yz):
    """
    计算极端降水指数
    data_all: (n_basins, days)  每个流域全年数据
    pre_yz: (n_basins, 3)       每个流域的90/95/99阈值
    返回: (n_basins, 10)         10个指标
    """
    n, days = data_all.shape
    outdata = np.full((n, 10), np.nan, dtype=np.float32)
    
    for i in range(n):
        row = data_all[i, :]
        yz90, yz95, yz99 = pre_yz[i, :]
        
        # R90pD, R90pTOT
        if np.isnan(yz90):
            R90pD = np.nan
            R90pTOT = np.nan
        else:
            mask90 = row > yz90
            R90pD = np.sum(mask90)
            R90pTOT = np.sum(row[mask90])
        
        # R95pD, R95pTOT
        if np.isnan(yz95):
            R95pD = np.nan
            R95pTOT = np.nan
        else:
            mask95 = row > yz95
            R95pD = np.sum(mask95)
            R95pTOT = np.sum(row[mask95])
        
        # R99pD, R99pTOT
        if np.isnan(yz99):
            R99pD = np.nan
            R99pTOT = np.nan
        else:
            mask99 = row > yz99
            R99pD = np.sum(mask99)
            R99pTOT = np.sum(row[mask99])
        
        # RX1-day
        RX1_day = np.nanmax(row)
        
        # R90pI, R95pI, R99pI (强度指标)
        R90pI = R90pTOT / R90pD if R90pD > 0 else np.nan
        R95pI = R95pTOT / R95pD if R95pD > 0 else np.nan
        R99pI = R99pTOT / R99pD if R99pD > 0 else np.nan
        
        # 组合结果
        outdata[i, 0] = R90pD
        outdata[i, 1] = R90pTOT
        outdata[i, 2] = R95pD
        outdata[i, 3] = R95pTOT
        outdata[i, 4] = R99pD
        outdata[i, 5] = R99pTOT
        outdata[i, 6] = R90pI
        outdata[i, 7] = R95pI
        outdata[i, 8] = R99pI
        outdata[i, 9] = RX1_day
    
    return outdata

def part1_calculate_epes():
    """第一部分：计算SSP情景下的极端降水指数"""
    print("=== 第一部分：计算SSP情景下的极端降水指数 ===")
    
    # 极端降水指数名称
    HW_indexs = ['R90pD', 'R90pTOT', 'R95pD', 'R95pTOT', 'R99pD', 'R99pTOT', 'R90pI', 'R95pI', 'R99pI', 'RX1-day']
    
    # 设置输入和输出目录
    input_dir = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03/pre_data/ssp/basins_daily_mean'
    yz_dir = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03/pre_data/historical/basins_yz'
    output_dir = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03/pre_data/ssp/basins_EPEs'
    
    # 如果输出目录不存在则创建
    os.makedirs(output_dir, exist_ok=True)
    
    # 读取阈值数据
    yz_file = os.path.join(yz_dir, 'basin_precipitation_thresholds2.csv')
    yz_table = pd.read_csv(yz_file)
    
    # 获取阈值数据
    pre_yz = yz_table[['P90', 'P95', 'P99']].values
    
    print('已读取流域降水阈值数据')
    
    # 定义SSP情景和年份范围
    ssps = ['ssp126', 'ssp245', 'ssp370', 'ssp585']
    models = ['ACCESS-CM2', 'ACCESS-ESM1-5', 'BCC-CSM2-MR', 'CanESM5', 'CMCC-ESM2', 
              'CNRM-CM6-1', 'CNRM-ESM2-1', 'EC-Earth3', 'EC-Earth3-Veg-LR', 'FGOALS-g3', 
              'GFDL-ESM4', 'GISS-E2-1-G', 'INM-CM4-8', 'INM-CM5-0', 'IPSL-CM6A-LR', 
              'MIROC6', 'MIROC-ES2L', 'MPI-ESM1-2-HR', 'MPI-ESM1-2-LR', 'MRI-ESM2-0',
              'NorESM2-LM', 'NorESM2-MM', 'TaiESM1']
    
    # 循环处理每个SSP情景
    for ssp in ssps:
        # 循环处理每个模型
        for model in models:
            print(f"处理模型: {model}, 情景: {ssp}")
            
            # 构建模型输入输出目录
            model_input_dir = os.path.join(input_dir, ssp, model)
            model_output_dir = os.path.join(output_dir, ssp, model)
            
            # 如果输出目录不存在则创建
            os.makedirs(model_output_dir, exist_ok=True)
            
            # 循环处理每一年
            for year in range(2021, 2101):
                # 构建输入文件名
                input_file = os.path.join(model_input_dir, f'basin_daily_means_{year}.csv')
                
                # 读取数据
                data = pd.read_csv(input_file)
                daily_data = data.iloc[:, 1:].values  # 跳过第一列BasinID
                
                # 计算极端降水指标
                outdata = compute_precip_indices(daily_data, pre_yz)
                
                # 创建表头
                headers = ['R90pD', 'R90pTOT', 'R95pD', 'R95pTOT', 'R99pD', 'R99pTOT', 'R90pI', 'R95pI', 'R99pI', 'RX1-day']
                
                # 将数据转换为DataFrame
                result_df = pd.DataFrame(outdata, columns=headers)
                
                # 构建输出文件路径
                output_file = os.path.join(model_output_dir, f'EPEs_{year}.csv')
                
                # 将DataFrame写入CSV文件
                result_df.to_csv(output_file, index=False)
                
                print(f'  已完成{model}_{ssp}_{year}年数据处理')

def part2_apply_correction():
    """第二部分：应用校正系数"""
    print("\n=== 第二部分：应用校正系数 ===")
    
    # 极端降水指数名称
    HW_indexs = ['R90pD', 'R90pTOT', 'R95pD', 'R95pTOT', 'R99pD', 'R99pTOT', 'R90pI', 'R95pI', 'R99pI', 'RX1-day']
    
    # 定义模型
    models = ['ACCESS-CM2', 'ACCESS-ESM1-5', 'BCC-CSM2-MR', 'CanESM5', 'CMCC-ESM2', 
              'CNRM-CM6-1', 'CNRM-ESM2-1', 'EC-Earth3', 'EC-Earth3-Veg-LR', 'FGOALS-g3', 
              'GFDL-ESM4', 'GISS-E2-1-G', 'INM-CM4-8', 'INM-CM5-0', 'IPSL-CM6A-LR', 
              'MIROC6', 'MIROC-ES2L', 'MPI-ESM1-2-HR', 'MPI-ESM1-2-LR', 'MRI-ESM2-0',
              'NorESM2-LM', 'NorESM2-MM', 'TaiESM1']
    
    # 定义SSP情景
    ssps = ['ssp126', 'ssp245', 'ssp370', 'ssp585']
    
    # 循环处理每个模型
    for model in models:
        print(f"处理模型: {model}")
        
        # 读取该模型的校正系数
        correction_file = f'/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03/pre_data/historical/basins_EPEs_models/{model}/correction_factors.csv'
        correction_table = pd.read_csv(correction_file)
        correction_factors = correction_table.values
        
        # 循环处理每个SSP情景
        for ssp in ssps:
            print(f"  处理情景: {ssp}")
            
            # 设置输入输出路径
            input_dir = f'/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03/pre_data/ssp/basins_EPEs/{ssp}/{model}'
            output_dir = f'/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03/pre_data/ssp/basins_EPEs_corrected/{ssp}/{model}'
            
            # 如果输出目录不存在则创建
            os.makedirs(output_dir, exist_ok=True)
            
            # 循环处理每一年
            for year in range(2021, 2101):
                # 读取原始EPEs数据
                input_file = os.path.join(input_dir, f'EPEs_{year}.csv')
                data_table = pd.read_csv(input_file)
                data = data_table.values
                
                # 应用校正系数
                corrected_data = data * correction_factors
                
                # 创建校正后的DataFrame
                corrected_df = pd.DataFrame(corrected_data, columns=HW_indexs)
                
                # 保存校正后的数据
                output_file = os.path.join(output_dir, f'EPEs_{year}.csv')
                corrected_df.to_csv(output_file, index=False)
                
                print(f'    已完成{model}模式{ssp}情景{year}年数据的校正')
    
    print('已完成所有模式和情景数据的校正')

# def part3_calculate_mme():
#     """第三部分：计算多模式集合平均值"""
#     print("\n=== 第三部分：计算多模式集合平均值 ===")
    
#     # 极端降水指数名称
#     HW_indexs = ['R90pD', 'R90pTOT', 'R95pD', 'R95pTOT', 'R99pD', 'R99pTOT', 'R90pI', 'R95pI', 'R99pI', 'RX1-day']
    
#     # 定义SSP情景和年份范围
#     ssps = ['ssp126', 'ssp245', 'ssp370', 'ssp585']
#     models = ['ACCESS-CM2', 'ACCESS-ESM1-5', 'BCC-CSM2-MR', 'CanESM5', 'CMCC-ESM2', 
#               'CNRM-CM6-1', 'CNRM-ESM2-1', 'EC-Earth3', 'EC-Earth3-Veg-LR', 'FGOALS-g3', 
#               'GFDL-ESM4', 'GISS-E2-1-G', 'INM-CM4-8', 'INM-CM5-0', 'IPSL-CM6A-LR', 
#               'MIROC6', 'MIROC-ES2L', 'MPI-ESM1-2-HR', 'MPI-ESM1-2-LR', 'MRI-ESM2-0',
#               'NorESM2-LM', 'NorESM2-MM', 'TaiESM1']
    
#     # 设置输入目录
#     input_dir = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03/pre_data/ssp/basins_EPEs_corrected'
    
#     # 循环处理每个SSP情景
#     for ssp in ssps:
#         print(f"处理SSP情景: {ssp}")
        
#         # 初始化存储所有模型数据的列表
#         all_models_data = []
        
#         # 循环处理每个模型
#         for model in models:
#             print(f"  读取模型: {model}")
            
#             # 构建模型输入目录
#             model_input_dir = os.path.join(input_dir, ssp, model)
            
#             # 初始化存储所有年份数据的列表
#             all_years_data = []
            
#             # 循环读取每一年的数据
#             for year in range(2021, 2101):
#                 # 构建输入文件路径
#                 input_file = os.path.join(model_input_dir, f'EPEs_{year}.csv')
                
#                 # 读取CSV文件
#                 data = pd.read_csv(input_file)
                
#                 # 将数据转换为数组
#                 year_data = data.values
                
#                 # 将年度数据添加到列表中
#                 all_years_data.append(year_data)
                
#                 print(f'    已读取{model}_{ssp}_{year}年校正后的未来极端降水数据')
            
#             # 将当前模型的所有年份数据转换为numpy数组并添加到所有模型数据中
#             model_data = np.array(all_years_data)  # (years, basins, indices)
#             all_models_data.append(model_data)
        
#         # 将所有模型数据转换为numpy数组
#         all_models_data = np.array(all_models_data)  # (models, years, basins, indices)
        
#         # 计算多模式平均值(MME)
#         mme_data = np.nanmean(all_models_data, axis=0)  # (years, basins, indices)
        
#         # 为MME创建输出目录
#         output_dir = os.path.join(input_dir, ssp, 'MME')
#         os.makedirs(output_dir, exist_ok=True)
        
#         # 保存MME数据
#         for i, year in enumerate(range(2021, 2101)):
#             year_data = mme_data[i, :, :]  # (basins, indices)
            
#             # 创建DataFrame
#             df = pd.DataFrame(year_data, columns=HW_indexs)
            
#             # 保存为CSV
#             output_file = os.path.join(output_dir, f'EPEs_{year}.csv')
#             df.to_csv(output_file, index=False)
            
#             print(f'  已保存{ssp}情景{year}年MME数据')

def main():
    """主函数"""
    part1_calculate_epes()
    part2_apply_correction()
    # part3_calculate_mme()
    print("\n=== 所有处理完成 ===")

if __name__ == "__main__":
    main()
