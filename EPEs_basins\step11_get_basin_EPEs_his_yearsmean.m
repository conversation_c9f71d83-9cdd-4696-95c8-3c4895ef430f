clc;clear;close all

addpath '/mnt/sdb2/yuanshuai/wanpan/yuan/ubuntu_code'

HW_indexs = {'R90pD','R90pTOT','R95pD','R95pTOT','R99pD','R99pTOT','R90pI','R95pI','R99pI','RX1-day'};


% 设置输入和输出目录
input_dir = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03/pre_data/historical/basins_EPEs';

% 初始化存储所有年份数据的矩阵
all_years_data = [];

% 循环读取每一年的数据
for y = 1971:2020
    % 构建输入文件路径
    input_file = fullfile(input_dir, sprintf('EPEs_%d.csv', y));
    
    % 读取CSV文件
    data = readtable(input_file);
    
    % 将数据转换为数组
    year_data = table2array(data);
    
    % 将年度数据添加到总矩阵中
    if isempty(all_years_data)
        all_years_data = zeros(size(year_data,1), size(year_data,2), 50); % 50年数据
    end
    all_years_data(:,:,y-1970) = year_data;
    
    fprintf('已读取%d年历史极端降水数据\n', y);
end

% 计算多年平均值
mean_data = mean(all_years_data, 3);

% 读取1971年的流域ID
basin_file = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03/pre_data/historical/basins_daily_mean/basin_daily_means_1971.csv';
basin_data = readtable(basin_file);
basin_ids = basin_data.BasinID;

% 将流域ID和平均值数据合并
data_with_ids = [basin_ids, mean_data];

% 创建列名
column_names = ['BasinID', HW_indexs];

% 转换为表格并添加列名
result_table = array2table(data_with_ids, 'VariableNames', column_names);

% 构建输出文件路径
output_file = fullfile(input_dir, 'EPEs_historical_basin_mean.csv');

% 保存为CSV文件
writetable(result_table, output_file);

fprintf('已完成历史极端降水指标多年平均值的计算和保存\n');

