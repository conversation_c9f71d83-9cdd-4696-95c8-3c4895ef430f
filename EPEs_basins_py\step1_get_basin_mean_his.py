import os
import numpy as np
import rasterio
from pathlib import Path
import h5py
import pandas as pd
import concurrent.futures
from functools import partial

def process_year(year, mask_data, m, n, basin_ids, output_dir):
    """处理单年数据的函数"""
    print(f"处理{year}年数据...")

    # 读取mat文件 - 使用h5py方式，参照EPEs_0.25的读取方式
    mat_path = f'/mnt/sdb2/yuanshuai/wanpan/yuan/ERA5-Land/fbl_025/mat/Pre/day/Pre_{year}.mat'

    with h5py.File(mat_path, 'r') as mat_data:
        pre_data = np.array(mat_data['Pre_year'])
        pre_data = pre_data.T  # 转置

    # 重塑数据，参照EPEs_0.25的处理方式
    pre_data_reshaped = pre_data.reshape(n, m, -1)  # 注意这里是n, m
    pre_data_reshaped = np.transpose(pre_data_reshaped, (1, 0, 2))  # (m, n, days)

    # 将数据拉平为 (m*n, days) 格式，与matlab的data_all对应
    data_all = pre_data_reshaped.reshape(m * n, -1)

    # 计算每个流域的逐日平均值
    basin_daily_means = np.zeros((len(basin_ids), 365))

    for b, basin_id in enumerate(basin_ids):
        # 获取当前流域的所有网格点
        basin_pixels = (mask_data == basin_id)

        # 计算该流域每天的均值
        for day in range(365):
            basin_daily_means[b, day] = np.nanmean(data_all[basin_pixels, day])

    # 准备CSV数据
    # 创建表头
    headers = ['BasinID'] + [f'Day_{day+1:03d}' for day in range(365)]

    # 创建数据矩阵
    csv_data = np.zeros((len(basin_ids), 366))  # 366列：1列BasinID + 365列数据
    csv_data[:, 0] = basin_ids  # 第一列是流域ID
    csv_data[:, 1:] = basin_daily_means  # 其余列是每日数据

    # 转换为DataFrame
    df = pd.DataFrame(csv_data, columns=headers)

    # 保存为CSV文件
    csv_filename = os.path.join(output_dir, f'basin_daily_means_{year}.csv')
    df.to_csv(csv_filename, index=False)

    print(f'已完成{year}年数据处理')
    return year

def main():
    # 文件路径设置
    geo_filename = '/mnt/sdb2/yuanshuai/wanpan/yuan/clip_biaozhun_tif/clip_tif_global_600_1440.tif'
    mask_geo_filename = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new02/shp/valid_pixels/updated_valid_pixels_basin_id.tif'

    # 读取地理信息和掩膜数据
    with rasterio.open(geo_filename) as src:
        m, n = src.height, src.width

    # 读入掩膜文件
    with rasterio.open(mask_geo_filename) as src:
        mask_data = src.read(1)

    # 将掩膜数据重塑为一维数组，与matlab中的reshape(mask_data,m*n,1)对应
    mask_data = mask_data.reshape(m * n)

    # 获取所有唯一的流域ID(排除0和NaN)
    basin_ids = np.unique(mask_data)
    basin_ids = basin_ids[basin_ids > 0]

    # 创建输出目录
    output_dir = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03/pre_data/historical/basins_daily_mean'
    os.makedirs(output_dir, exist_ok=True)

    # 年份列表
    years = list(range(1971, 2021))

    # 使用多线程处理
    print(f"开始使用多线程处理{len(years)}年的数据...")

    # 创建部分函数，固定参数
    process_func = partial(process_year,
                          mask_data=mask_data,
                          m=m,
                          n=n,
                          basin_ids=basin_ids,
                          output_dir=output_dir)

    # 使用线程池执行
    with concurrent.futures.ThreadPoolExecutor(max_workers=25) as executor:
        # 提交所有任务
        future_to_year = {executor.submit(process_func, year): year for year in years}

        # 获取结果
        completed_years = []
        for future in concurrent.futures.as_completed(future_to_year):
            year = future_to_year[future]
            try:
                result = future.result()
                completed_years.append(result)
                print(f"线程完成: {result}年")
            except Exception as exc:
                print(f'{year}年处理出现异常: {exc}')

    print(f"所有年份处理完成，共处理了{len(completed_years)}年的数据")

if __name__ == "__main__":
    main()
