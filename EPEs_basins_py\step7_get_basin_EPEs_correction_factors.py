import os
import numpy as np
import pandas as pd

def main():
    # 极端降水指数名称
    HW_indexs = ['R90pD', 'R90pTOT', 'R95pD', 'R95pTOT', 'R99pD', 'R99pTOT', 'R90pI', 'R95pI', 'R99pI', 'RX1-day']
    
    # 定义模型
    models = ['ACCESS-CM2', 'ACCESS-ESM1-5', 'BCC-CSM2-MR', 'CanESM5', 'CMCC-ESM2', 
              'CNRM-CM6-1', 'CNRM-ESM2-1', 'EC-Earth3', 'EC-Earth3-Veg-LR', 'FGOALS-g3', 
              'GFDL-ESM4', 'GISS-E2-1-G', 'INM-CM4-8', 'INM-CM5-0', 'IPSL-CM6A-LR', 
              'MIROC6', 'MIROC-ES2L', 'MPI-ESM1-2-HR', 'MPI-ESM1-2-LR', 'MRI-ESM2-0',
              'NorESM2-LM', 'NorESM2-MM', 'TaiESM1']
    
    # 设置输入目录
    input_dir = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03/pre_data/historical/basins_EPEs'
    
    # 读取第一年数据获取流域数量
    first_file = os.path.join(input_dir, 'EPEs_1971.csv')
    T_first = pd.read_csv(first_file)
    num_basins = len(T_first)
    
    # 初始化存储所有年份数据的数组
    yearly_data = np.zeros((44, num_basins, len(HW_indexs)))  # 1971-2014年,共44年
    years = list(range(1971, 2015))
    
    # 循环读取每年的数据
    for i, year in enumerate(years):
        print(f"读取{year}年观测数据...")
        
        # 构建输入文件路径
        input_file = os.path.join(input_dir, f'EPEs_{year}.csv')
        
        # 读取CSV文件
        df = pd.read_csv(input_file)
        
        # 存储数据 - 保持流域维度
        yearly_data[i, :, :] = df.values
    
    # 计算多年平均值 - 对年份维度取平均
    historical_means = np.mean(yearly_data, axis=0)
    
    print('已完成历史数据的读取')
    
    # 读取观测数据的多年平均值
    obs_means = historical_means
    
    # 设置校正系数的上下限
    min_correction = 0.1   # 最小校正系数
    max_correction = 10.0  # 最大校正系数
    
    # 循环处理每个模型
    for model in models:
        print(f"处理模型: {model}")
        
        # 设置输入路径
        model_input_dir = f'/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03/pre_data/historical/basins_EPEs_models/{model}'
        
        # 初始化存储该模式所有年份数据的矩阵
        model_yearly_data = np.zeros((len(years), num_basins, len(HW_indexs)))
        
        # 循环读取每年的数据
        for i, year in enumerate(years):
            # 构建输入文件路径
            input_file = os.path.join(model_input_dir, f'EPEs_{year}.csv')
            
            # 读取CSV文件
            df = pd.read_csv(input_file)
            
            # 存储数据 - 保持流域维度
            model_yearly_data[i, :, :] = df.values
        
        # 计算模式的多年平均值 - 对年份维度取平均
        model_means = np.mean(model_yearly_data, axis=0)
        
        # 初始化校正系数数组
        correction_factors = np.zeros((num_basins, len(HW_indexs)))
        
        # 计算每个流域的校正系数并进行限制
        for basin in range(num_basins):
            for idx in range(len(HW_indexs)):
                if model_means[basin, idx] > 0 and obs_means[basin, idx] > 0:
                    # 正常计算校正系数
                    cf = obs_means[basin, idx] / model_means[basin, idx]
                    
                    # 限制校正系数在设定范围内
                    cf = max(min_correction, min(cf, max_correction))
                    
                    correction_factors[basin, idx] = cf
                elif model_means[basin, idx] == 0 and obs_means[basin, idx] == 0:
                    # 如果两者都为0，设置校正系数为1
                    correction_factors[basin, idx] = 1
                elif model_means[basin, idx] == 0:
                    # 如果模式值为0但观测值不为0，设置为最大校正系数
                    correction_factors[basin, idx] = max_correction
                else:
                    # 如果观测值为0但模式值不为0，设置为最小校正系数
                    correction_factors[basin, idx] = min_correction
        
        # 创建校正系数DataFrame
        correction_df = pd.DataFrame(correction_factors, columns=HW_indexs)
        
        # 保存校正系数
        output_file = os.path.join(model_input_dir, 'correction_factors.csv')
        correction_df.to_csv(output_file, index=False)
        
        print(f'已完成{model}模式的校正系数计算和保存')
    
    print('已完成所有模式的校正系数计算')

if __name__ == "__main__":
    main()
