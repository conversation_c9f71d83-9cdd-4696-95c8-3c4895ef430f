clc;clear;close all

% 设置输入和输出目录
input_dir = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03/pre_data/historical/basins_daily_mean';
output_dir = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03/pre_data/historical/basins_yz';

% 如果输出目录不存在则创建
if ~exist(output_dir, 'dir')
    mkdir(output_dir);
end

% 初始化存储所有年份数据的数组
all_years_data = [];
basin_ids = [];

% 循环处理每一年的数据
for y = 1971:2020
    y_str = num2str(y);

    % 构建输入CSV文件路径
    csv_filename = fullfile(input_dir, sprintf('basin_daily_means_%s.csv', y_str));

    % 读取CSV文件
    T = readtable(csv_filename);

    % 获取流域ID（只在第一年获取）
    if y == 1971
        basin_ids = T.BasinID;
    end

    % 获取日降水数据(不包括BasinID列)
    daily_data = table2array(T(:,2:end));

    % 将数据添加到总数组中 - 按列连接（水平拼接）
    all_years_data = [all_years_data, daily_data];

    fprintf('已读取%d年数据\n', y);
end

% 现在all_years_data的形状是 (n_basins, n_years * 365)
% 转置以便按流域索引
all_years_data = all_years_data';  % 转置为 (n_years * 365, n_basins)

% 计算每个流域的分位数阈值
percentiles = [90, 95, 99];
thresholds = zeros(length(basin_ids), length(percentiles));

for i = 1:length(basin_ids)
    % 获取第i个流域的所有数据（所有年份所有天数）
    basin_data = all_years_data(:, i);
    % 只选择降水量大于1的值
    valid_data = basin_data(basin_data > 1);
    if ~isempty(valid_data)
        thresholds(i, :) = prctile(valid_data, percentiles);
    else
        thresholds(i, :) = NaN;
    end
end

% 创建结果表格
result_table = array2table(thresholds, 'VariableNames', {'P90', 'P95', 'P99'});
result_table.BasinID = basin_ids;

% 保存结果
output_file = fullfile(output_dir, 'basin_precipitation_thresholds2.csv');
writetable(result_table, output_file);

fprintf('已完成所有流域降水阈值计算，结果已保存至：%s\n', output_file);