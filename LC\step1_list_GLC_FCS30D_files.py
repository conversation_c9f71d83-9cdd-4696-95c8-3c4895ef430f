import os
import pandas as pd
from pathlib import Path
import re

def check_path_permissions(path):
    """
    检查路径的读写权限
    """
    try:
        # 检查目录是否可写
        if os.path.isdir(path):
            test_file = os.path.join(path, 'test_write_permission.tmp')
            try:
                with open(test_file, 'w') as f:
                    f.write('test')
                os.remove(test_file)
                return True, "目录可写"
            except Exception as e:
                return False, f"目录不可写: {e}"
        else:
            # 检查父目录是否可写
            parent_dir = os.path.dirname(path)
            if os.path.exists(parent_dir):
                return check_path_permissions(parent_dir)
            else:
                return False, "父目录不存在"
    except Exception as e:
        return False, f"权限检查失败: {e}"

def parse_filename(filename):
    """
    解析GLC_FCS30D文件名，提取时间范围和地理坐标信息

    文件名格式: GLC_FCS30D_yyyyYYYY_E/W**N/S**_type_V1.1.tif
    例如: GLC_FCS30D_19852000_E0N10_5years_V1.1.tif, GLC_FCS30D_20002022_E0N10_Annual_V1.1.tif
    """
    # 使用正则表达式解析文件名
    pattern = r'GLC_FCS30D_(\d{4})(\d{4})_([EW])(\d+)([NS])(\d+)_(\w+)_V(\d+\.\d+)\.tif'
    match = re.match(pattern, filename)

    if match:
        start_year = int(match.group(1))
        end_year = int(match.group(2))
        lon_dir = match.group(3)  # E或W
        lon_val = int(match.group(4))
        lat_dir = match.group(5)  # N或S
        lat_val = int(match.group(6))
        data_type = match.group(7)  # 5years或Annual
        version = match.group(8)  # 版本号

        # 计算实际经纬度范围
        if lon_dir == 'W':
            lon_start = -lon_val
        else:
            lon_start = lon_val
        lon_end = lon_start + 5

        if lat_dir == 'S':
            lat_start = -lat_val - 5  # 注意：文件名中的坐标是左上角
        else:
            lat_start = lat_val - 5
        lat_end = lat_start + 5

        return {
            'start_year': start_year,
            'end_year': end_year,
            'lon_start': lon_start,
            'lon_end': lon_end,
            'lat_start': lat_start,
            'lat_end': lat_end,
            'coordinate_code': f"{lon_dir}{match.group(4)}{lat_dir}{match.group(6)}",
            'data_type': data_type,
            'version': version
        }
    else:
        return None

def scan_directory(root_path):
    """
    扫描指定目录下的所有GLC_FCS30D文件
    """
    root_path = Path(root_path)
    file_info_list = []
    directory_stats = {}

    print(f"开始扫描目录: {root_path}")
    print("=" * 80)

    # 首先扫描所有子目录
    subdirs = [d for d in root_path.iterdir() if d.is_dir()]
    print(f"发现 {len(subdirs)} 个子目录:")
    for subdir in sorted(subdirs):
        print(f"  - {subdir.name}")
    print("-" * 80)

    # 遍历所有子目录和文件
    for item in root_path.rglob("*"):
        if item.is_file() and item.name.startswith("GLC_FCS30D_") and item.name.endswith(".tif"):
            # 解析文件名
            parsed_info = parse_filename(item.name)

            if parsed_info:
                # 统计每个目录的文件数量
                parent_dir = item.parent.name
                if parent_dir not in directory_stats:
                    directory_stats[parent_dir] = 0
                directory_stats[parent_dir] += 1

                file_info = {
                    'filename': item.name,
                    'full_path': str(item),
                    'relative_path': str(item.relative_to(root_path)),
                    'parent_directory': parent_dir,
                    'file_size_mb': round(item.stat().st_size / (1024 * 1024), 2),
                    **parsed_info
                }
                file_info_list.append(file_info)

                print(f"发现文件: {item.name}")
                print(f"  目录: {parent_dir}")
                print(f"  时间范围: {parsed_info['start_year']}-{parsed_info['end_year']}")
                print(f"  数据类型: {parsed_info['data_type']}")
                print(f"  版本: {parsed_info['version']}")
                print(f"  地理范围: 经度 {parsed_info['lon_start']}°-{parsed_info['lon_end']}°, "
                      f"纬度 {parsed_info['lat_start']}°-{parsed_info['lat_end']}°")
                print(f"  文件大小: {file_info['file_size_mb']} MB")
                print("-" * 40)

    # 显示目录统计
    print(f"\n目录文件统计:")
    for directory, count in sorted(directory_stats.items()):
        print(f"  {directory}: {count} 个文件")

    return file_info_list

def analyze_dataset(file_info_list):
    """
    分析数据集的统计信息
    """
    if not file_info_list:
        print("未找到任何GLC_FCS30D文件！")
        return

    df = pd.DataFrame(file_info_list)

    print("\n" + "=" * 80)
    print("数据集统计信息")
    print("=" * 80)

    print(f"总文件数量: {len(file_info_list)}")
    print(f"总文件大小: {df['file_size_mb'].sum():.2f} MB ({df['file_size_mb'].sum()/1024:.2f} GB)")

    # 时间范围和数据类型统计
    print(f"\n时间范围和数据类型统计:")
    time_type_stats = df.groupby(['start_year', 'end_year', 'data_type']).size()
    for (start, end, data_type), count in time_type_stats.items():
        if start == end:
            print(f"  {start}年 ({data_type}): {count} 个瓦片")
        else:
            print(f"  {start}-{end}年 ({data_type}): {count} 个瓦片")

    # 版本统计
    print(f"\n版本统计:")
    version_stats = df['version'].value_counts()
    for version, count in version_stats.items():
        print(f"  V{version}: {count} 个文件")

    # 地理分布统计
    print(f"\n地理分布统计:")
    print(f"  经度范围: {df['lon_start'].min()}° 到 {df['lon_end'].max()}°")
    print(f"  纬度范围: {df['lat_start'].min()}° 到 {df['lat_end'].max()}°")
    print(f"  唯一坐标瓦片数: {df['coordinate_code'].nunique()}")

    # 目录分布统计（按地理区域）
    print(f"\n目录分布统计（按地理区域）:")
    dir_stats = df['parent_directory'].value_counts().sort_index()

    # 解析目录名中的地理信息
    print(f"  总共 {len(dir_stats)} 个地理区域目录:")
    for directory, count in dir_stats.items():
        # 尝试从目录名中提取地理范围信息
        if 'maps_' in directory:
            geo_part = directory.split('maps_')[-1]
            print(f"    {directory}: {count} 个文件 (区域: {geo_part})")
        else:
            print(f"    {directory}: {count} 个文件")

    # 检查数据完整性
    print(f"\n数据完整性检查:")
    expected_files_per_region = len(time_type_stats)  # 每个地理区域应该有的文件数
    incomplete_regions = []
    for directory, count in dir_stats.items():
        if count != expected_files_per_region:
            incomplete_regions.append((directory, count, expected_files_per_region))

    if incomplete_regions:
        print(f"  发现 {len(incomplete_regions)} 个区域的文件不完整:")
        for directory, actual, expected in incomplete_regions:
            print(f"    {directory}: 实际 {actual} 个文件，期望 {expected} 个文件")
    else:
        print(f"  所有地理区域的文件都完整 (每个区域 {expected_files_per_region} 个文件)")

def analyze_directory_structure(file_info_list):
    """
    专门分析目录结构和地理分布
    """
    if not file_info_list:
        return

    df = pd.DataFrame(file_info_list)

    print("\n" + "=" * 80)
    print("目录结构和地理分布详细分析")
    print("=" * 80)

    # 按目录分组分析
    dir_groups = df.groupby('parent_directory')

    print(f"总共发现 {len(dir_groups)} 个地理区域目录:")
    print()

    for directory, group in dir_groups:
        print(f"目录: {directory}")
        print(f"  文件数量: {len(group)}")
        print(f"  文件大小: {group['file_size_mb'].sum():.2f} MB")

        # 分析该目录下的时间范围和数据类型
        time_type_ranges = group.groupby(['start_year', 'end_year', 'data_type']).size()
        print(f"  时间范围和数据类型:")
        for (start, end, data_type), count in time_type_ranges.items():
            if start == end:
                print(f"    {start}年 ({data_type}): {count} 个文件")
            else:
                print(f"    {start}-{end}年 ({data_type}): {count} 个文件")

        # 分析地理范围
        if len(group) > 0:
            lon_range = f"{group['lon_start'].iloc[0]}° 到 {group['lon_end'].iloc[0]}°"
            lat_range = f"{group['lat_start'].iloc[0]}° 到 {group['lat_end'].iloc[0]}°"
            print(f"  地理范围: 经度 {lon_range}, 纬度 {lat_range}")

        print("-" * 60)

def save_file_list(file_info_list, output_path):
    """
    保存文件列表到CSV文件
    """
    if not file_info_list:
        print("没有文件信息可保存！")
        return

    try:
        df = pd.DataFrame(file_info_list)

        # 重新排列列的顺序
        columns_order = [
            'filename', 'relative_path', 'parent_directory',
            'start_year', 'end_year', 'data_type', 'version', 'coordinate_code',
            'lon_start', 'lon_end', 'lat_start', 'lat_end',
            'file_size_mb', 'full_path'
        ]

        df = df[columns_order]
        df = df.sort_values(['start_year', 'end_year', 'coordinate_code'])

        # 检查输出目录是否存在
        output_dir = os.path.dirname(output_path)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
            print(f"创建输出目录: {output_dir}")

        # 保存文件
        df.to_csv(output_path, index=False, encoding='utf-8')
        print(f"\n✓ 文件列表已成功保存到: {output_path}")
        print(f"  - 总共保存了 {len(df)} 条文件记录")
        print(f"  - 文件大小: {os.path.getsize(output_path) / 1024:.2f} KB")

    except Exception as e:
        print(f"\n✗ 保存文件时出错:")
        print(f"  错误信息: {e}")
        print(f"  输出路径: {output_path}")
        return

def main():
    # 设置数据路径
    data_path = "/mnt/sdb2/yuanshuai/wanpan/yuan/LC/GLC_FCS30D/ori_data"
    output_csv = "/mnt/sdb2/yuanshuai/wanpan/yuan/LC/GLC_FCS30D/data_info/GLC_FCS30D_file_inventory.csv"

    print("GLC_FCS30D 数据集文件扫描工具")
    print("=" * 80)
    print("数据说明:")
    print("- GLC_FCS30D包含961个5°×5°独立瓦片")
    print("- 文件命名格式: GLC_FCS30D_yyyyYYYY_E/W**N/S**.tif")
    print("- 时间范围: 1985-2022年，包含26个时间步")
    print("- 2000年前每5年更新，2000年后每年更新")
    print("=" * 80)

    # 检查输入路径是否存在
    if not os.path.exists(data_path):
        print(f"错误: 数据路径不存在 - {data_path}")
        return

    # 检查输出路径是否存在，如果不存在则创建
    output_dir = os.path.dirname(output_csv)
    if not os.path.exists(output_dir):
        print(f"输出目录不存在，正在创建: {output_dir}")
        try:
            os.makedirs(output_dir, exist_ok=True)
            print(f"✓ 成功创建输出目录: {output_dir}")
        except Exception as e:
            print(f"✗ 错误: 无法创建输出目录 {output_dir}")
            print(f"  错误信息: {e}")
            return
    else:
        print(f"✓ 输出目录已存在: {output_dir}")

    # 检查输出路径的写入权限
    can_write, msg = check_path_permissions(output_dir)
    if not can_write:
        print(f"✗ 错误: 输出目录权限不足")
        print(f"  {msg}")
        return
    else:
        print(f"✓ 输出目录权限检查通过: {msg}")

    print(f"输出文件将保存到: {output_csv}")
    print("-" * 80)
    
    # 扫描文件
    file_info_list = scan_directory(data_path)

    # 分析数据集
    analyze_dataset(file_info_list)

    # 详细分析目录结构
    analyze_directory_structure(file_info_list)

    # 保存文件列表
    save_file_list(file_info_list, output_csv)

    # 验证输出文件是否成功创建
    if os.path.exists(output_csv):
        file_size = os.path.getsize(output_csv)
        print(f"\n✓ 扫描完成！输出文件已成功创建")
        print(f"  文件路径: {output_csv}")
        print(f"  文件大小: {file_size} 字节 ({file_size/1024:.2f} KB)")
    else:
        print(f"\n✗ 警告: 输出文件未能成功创建")
        print(f"  预期路径: {output_csv}")

if __name__ == "__main__":
    main()
