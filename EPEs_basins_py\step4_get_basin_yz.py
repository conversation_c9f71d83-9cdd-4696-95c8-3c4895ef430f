import os
import numpy as np
import pandas as pd

def main():
    # 设置输入和输出目录
    input_dir = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03/pre_data/historical/basins_daily_mean'
    output_dir = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03/pre_data/historical/basins_yz'
    
    # 如果输出目录不存在则创建
    os.makedirs(output_dir, exist_ok=True)
    
    # 初始化存储所有年份数据的列表
    all_years_data = []
    basin_ids = None
    
    # 循环处理每一年的数据
    for year in range(1971, 2021):
        print(f"读取{year}年数据...")
        
        # 构建输入CSV文件路径
        csv_filename = os.path.join(input_dir, f'basin_daily_means_{year}.csv')
        
        # 读取CSV文件
        df = pd.read_csv(csv_filename)
        
        # 获取流域ID（只在第一年获取）
        if year == 1971:
            basin_ids = df['BasinID'].values
        
        # 获取日降水数据(不包括BasinID列)
        daily_data = df.iloc[:, 1:].values  # 跳过第一列BasinID
        
        # 将数据添加到总列表中
        all_years_data.append(daily_data)
        
        print(f'已读取{year}年数据')
    
    # 将所有年份数据合并为一个数组
    # all_years_data: list of (n_basins, 365) -> (n_years * 365, n_basins)
    all_years_data = np.concatenate(all_years_data, axis=1)  # 沿着天数维度连接
    all_years_data = all_years_data.T  # 转置为 (n_days_total, n_basins)

    # 计算每个流域的分位数阈值
    percentiles = [90, 95, 99]
    thresholds = np.zeros((len(basin_ids), len(percentiles)))

    for i in range(len(basin_ids)):
        basin_data = all_years_data[:, i]
        # 只选择降水量大于1的值
        valid_data = basin_data[basin_data > 1]
        if len(valid_data) > 0:
            thresholds[i, :] = np.percentile(valid_data, percentiles)
        else:
            thresholds[i, :] = np.nan
    
    # 创建结果DataFrame
    result_df = pd.DataFrame(thresholds, columns=['P90', 'P95', 'P99'])
    result_df['BasinID'] = basin_ids
    
    # 重新排列列的顺序，将BasinID放在第一列
    result_df = result_df[['BasinID', 'P90', 'P95', 'P99']]
    
    # 保存结果
    output_file = os.path.join(output_dir, 'basin_precipitation_thresholds2.csv')
    result_df.to_csv(output_file, index=False)
    
    print(f'已完成所有流域降水阈值计算，结果已保存至：{output_file}')

if __name__ == "__main__":
    main()
