import os
import numpy as np
import pandas as pd
import rasterio
from rasterio.mask import mask
from rasterio.warp import calculate_default_transform, reproject, Resampling
from osgeo import gdal
import subprocess
from concurrent.futures import ProcessPoolExecutor
import glob
from pathlib import Path
import re

def parse_lc_filename(filename):
    """
    解析GLC_FCS30D文件名，提取地理坐标信息和波段信息
    """
    pattern = r'GLC_FCS30D_(\d{4})(\d{4})_([EW])(\d+)([NS])(\d+)_(\w+)_V(\d+\.\d+)\.tif'
    match = re.match(pattern, filename)

    if match:
        start_year = int(match.group(1))
        end_year = int(match.group(2))
        lon_dir = match.group(3)
        lon_val = int(match.group(4))
        lat_dir = match.group(5)
        lat_val = int(match.group(6))
        data_type = match.group(7)  # 5years或Annual

        # 计算实际经纬度范围
        if lon_dir == 'W':
            lon_start = -lon_val
        else:
            lon_start = lon_val
        lon_end = lon_start + 5

        if lat_dir == 'S':
            lat_start = -lat_val - 5
        else:
            lat_start = lat_val - 5
        lat_end = lat_start + 5

        # 根据数据类型确定年份和波段对应关系
        if data_type == '5years':
            # 1985-2000年，3个波段：1985, 1990, 1995
            year_band_map = {1985: 1, 1990: 2, 1995: 3}
        elif data_type == 'Annual':
            # 2000-2022年，23个波段：2000-2022
            year_band_map = {year: year - 2000 + 1 for year in range(2000, 2023)}
        else:
            year_band_map = {}

        return {
            'start_year': start_year,
            'end_year': end_year,
            'lon_start': lon_start,
            'lon_end': lon_end,
            'lat_start': lat_start,
            'lat_end': lat_end,
            'data_type': data_type,
            'year_band_map': year_band_map
        }
    return None

def get_dem_bounds(dem_file):
    """
    获取DEM文件的地理边界
    """
    with rasterio.open(dem_file) as src:
        bounds = src.bounds
        return {
            'left': bounds.left,
            'bottom': bounds.bottom,
            'right': bounds.right,
            'top': bounds.top
        }

def find_overlapping_lc_files(dem_bounds, lc_data_dir, year):
    """
    查找与DEM边界重叠的LC数据文件，返回文件路径和对应的波段号
    """
    overlapping_files = []

    # 遍历所有LC数据目录
    for subdir in os.listdir(lc_data_dir):
        subdir_path = os.path.join(lc_data_dir, subdir)
        if not os.path.isdir(subdir_path):
            continue

        # 查找该目录下的LC文件
        for filename in os.listdir(subdir_path):
            if filename.endswith('.tif') and filename.startswith('GLC_FCS30D_'):
                lc_info = parse_lc_filename(filename)
                if lc_info is None:
                    continue

                # 检查时间范围是否包含目标年份
                if not (lc_info['start_year'] <= year <= lc_info['end_year']):
                    continue

                # 检查该年份是否有对应的波段
                if year not in lc_info['year_band_map']:
                    continue

                # 检查地理范围是否重叠
                if (dem_bounds['left'] < lc_info['lon_end'] and
                    dem_bounds['right'] > lc_info['lon_start'] and
                    dem_bounds['bottom'] < lc_info['lat_end'] and
                    dem_bounds['top'] > lc_info['lat_start']):

                    file_path = os.path.join(subdir_path, filename)
                    band_number = lc_info['year_band_map'][year]
                    overlapping_files.append((file_path, band_number))

    return overlapping_files

def mosaic_lc_files(lc_files_with_bands, output_file):
    """
    镶嵌多个LC文件的指定波段为一个单波段文件
    lc_files_with_bands: [(file_path, band_number), ...]
    """
    if len(lc_files_with_bands) == 1:
        # 如果只有一个文件，提取指定波段
        file_path, band_number = lc_files_with_bands[0]
        with rasterio.open(file_path) as src:
            # 读取指定波段
            band_data = src.read(band_number)

            # 更新元数据为单波段
            out_meta = src.meta.copy()
            out_meta.update({
                "driver": "GTiff",
                "count": 1,
                "compress": "lzw"
            })

            # 保存单波段数据
            with rasterio.open(output_file, "w", **out_meta) as dest:
                dest.write(band_data, 1)

        return output_file

    # 多个文件需要镶嵌
    src_files_to_mosaic = []
    bands_to_mosaic = []

    for file_path, band_number in lc_files_with_bands:
        src = rasterio.open(file_path)
        src_files_to_mosaic.append(src)
        bands_to_mosaic.append(band_number)

    # 读取每个文件的指定波段
    arrays_to_mosaic = []
    for i, src in enumerate(src_files_to_mosaic):
        band_data = src.read(bands_to_mosaic[i])
        arrays_to_mosaic.append(band_data)

    # 手动镶嵌（因为rasterio.merge不支持不同波段）
    # 这里简化处理，假设所有文件有相同的投影和分辨率
    # 实际应用中可能需要更复杂的镶嵌逻辑

    # 获取所有文件的边界
    bounds_list = [src.bounds for src in src_files_to_mosaic]

    # 计算总边界
    left = min(bounds.left for bounds in bounds_list)
    bottom = min(bounds.bottom for bounds in bounds_list)
    right = max(bounds.right for bounds in bounds_list)
    top = max(bounds.top for bounds in bounds_list)

    # 使用第一个文件的变换和分辨率
    ref_src = src_files_to_mosaic[0]
    transform = ref_src.transform

    # 计算输出尺寸
    width = int((right - left) / abs(transform[0]))
    height = int((top - bottom) / abs(transform[4]))

    # 创建输出数组
    out_transform = rasterio.transform.from_bounds(left, bottom, right, top, width, height)

    # 获取元数据
    out_meta = ref_src.meta.copy()
    out_meta.update({
        "driver": "GTiff",
        "height": height,
        "width": width,
        "count": 1,
        "transform": out_transform,
        "compress": "lzw"
    })

    # 使用gdal_merge进行镶嵌（更可靠）
    temp_files = []
    for i, (file_path, band_number) in enumerate(lc_files_with_bands):
        temp_single_band = output_file.replace('.tif', f'_temp_band_{i}.tif')

        # 提取单波段
        gdal_command = [
            'gdal_translate',
            '-b', str(band_number),
            '-of', 'GTiff',
            '-co', 'COMPRESS=LZW',
            file_path,
            temp_single_band
        ]
        subprocess.run(gdal_command, check=True)
        temp_files.append(temp_single_band)

    # 镶嵌所有单波段文件
    merge_command = ['gdal_merge.py', '-o', output_file, '-of', 'GTiff', '-co', 'COMPRESS=LZW'] + temp_files
    subprocess.run(merge_command, check=True)

    # 清理临时文件
    for temp_file in temp_files:
        if os.path.exists(temp_file):
            os.remove(temp_file)

    # 关闭文件
    for src in src_files_to_mosaic:
        src.close()

    return output_file

def clip_lc_with_dem(dem_file, lc_file, output_file):
    """
    使用DEM文件掩膜LC数据
    """
    # 读取DEM文件获取掩膜
    with rasterio.open(dem_file) as dem_src:
        dem_data = dem_src.read(1)
        dem_bounds = dem_src.bounds
        dem_crs = dem_src.crs
        dem_transform = dem_src.transform
    
    # 创建掩膜（DEM有效值的区域）
    mask_array = (dem_data != dem_src.nodata) & (~np.isnan(dem_data))
    
    # 读取LC文件
    with rasterio.open(lc_file) as lc_src:
        # 重投影LC数据到DEM的坐标系和分辨率
        if lc_src.crs != dem_crs:
            # 计算重投影参数
            transform, width, height = calculate_default_transform(
                lc_src.crs, dem_crs, lc_src.width, lc_src.height, *lc_src.bounds)
            
            # 创建临时重投影文件
            temp_reproj = output_file.replace('.tif', '_temp_reproj.tif')
            
            with rasterio.open(temp_reproj, 'w',
                             driver='GTiff',
                             height=height,
                             width=width,
                             count=1,
                             dtype=lc_src.dtypes[0],
                             crs=dem_crs,
                             transform=transform,
                             compress='lzw') as dst:
                
                reproject(
                    source=rasterio.band(lc_src, 1),
                    destination=rasterio.band(dst, 1),
                    src_transform=lc_src.transform,
                    src_crs=lc_src.crs,
                    dst_transform=transform,
                    dst_crs=dem_crs,
                    resampling=Resampling.nearest)
            
            lc_file = temp_reproj
    
    # 使用gdal进行精确的重采样和裁剪
    gdal_command = [
        'gdalwarp',
        '-te', str(dem_bounds.left), str(dem_bounds.bottom), 
               str(dem_bounds.right), str(dem_bounds.top),
        '-tr', str(abs(dem_transform[0])), str(abs(dem_transform[4])),
        '-r', 'near',
        '-of', 'GTiff',
        '-co', 'COMPRESS=LZW',
        lc_file,
        output_file
    ]
    
    subprocess.run(gdal_command, check=True)
    
    # 应用DEM掩膜
    with rasterio.open(output_file, 'r+') as dst:
        lc_data = dst.read(1)
        lc_data[~mask_array] = dst.nodata if dst.nodata is not None else 0
        dst.write(lc_data, 1)
    
    # 清理临时文件
    if 'temp_reproj' in lc_file and os.path.exists(lc_file):
        os.remove(lc_file)
    
    return output_file

def process_single_basin(args):
    """
    处理单个流域的LC数据裁剪
    """
    dem_file, year, lc_data_dir, output_dir, temp_dir = args
    
    try:
        # 提取流域ID
        basin_id = os.path.basename(dem_file).replace('basin_', '').replace('_DEM.tif', '')
        
        print(f"开始处理流域 {basin_id}，年份 {year}")
        
        # 创建年份输出目录
        year_output_dir = os.path.join(output_dir, str(year))
        os.makedirs(year_output_dir, exist_ok=True)
        
        # 输出文件路径
        output_file = os.path.join(year_output_dir, f"basin_{basin_id}_LC.tif")
        
        # 如果输出文件已存在，跳过
        if os.path.exists(output_file):
            print(f"流域 {basin_id} 年份 {year} 的LC数据已存在，跳过")
            return
        
        # 获取DEM边界
        dem_bounds = get_dem_bounds(dem_file)
        
        # 查找重叠的LC文件（返回文件路径和波段号）
        overlapping_lc_files = find_overlapping_lc_files(dem_bounds, lc_data_dir, year)

        if not overlapping_lc_files:
            print(f"警告: 流域 {basin_id} 年份 {year} 未找到重叠的LC数据")
            return

        print(f"流域 {basin_id} 年份 {year} 找到 {len(overlapping_lc_files)} 个重叠的LC文件")
        for file_path, band_num in overlapping_lc_files:
            print(f"  - {os.path.basename(file_path)} (波段 {band_num})")

        # 临时镶嵌文件
        temp_mosaic = os.path.join(temp_dir, f"temp_mosaic_{basin_id}_{year}.tif")

        # 镶嵌LC文件（提取指定年份的波段）
        mosaic_lc_files(overlapping_lc_files, temp_mosaic)
        
        # 使用DEM掩膜LC数据
        clip_lc_with_dem(dem_file, temp_mosaic, output_file)
        
        # 清理临时文件
        if os.path.exists(temp_mosaic):
            os.remove(temp_mosaic)
        
        print(f"完成流域 {basin_id} 年份 {year} 的LC数据处理")
        
    except Exception as e:
        print(f"处理流域 {basin_id} 年份 {year} 时出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    # 设置路径
    dem_data_dir = "/mnt/sdb2/yuanshuai/wanpan/yuan/paper3/FSM/basins_DEM_lev05/Fathom"
    lc_data_dir = "/mnt/sdb2/yuanshuai/wanpan/yuan/LC/GLC_FCS30D/ori_data"
    output_dir = "/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03/FSM/LC/historical"
    temp_dir = "/mnt/sdb3/yuanshuai/temp_Q"

    print("GLC_FCS30D 流域LC数据裁剪工具")
    print("=" * 60)
    print("数据说明:")
    print("- 5years文件 (1985-2000): 3个波段 (1985, 1990, 1995)")
    print("- Annual文件 (2000-2022): 23个波段 (2000-2022年)")
    print("- 输出: 每个流域每年一个单波段LC数据")
    print("=" * 60)

    # 创建输出和临时目录
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(temp_dir, exist_ok=True)
    print(f"输出目录: {output_dir}")
    print(f"临时目录: {temp_dir}")
    
    # 获取所有DEM文件
    dem_files = glob.glob(os.path.join(dem_data_dir, "basin_*_DEM.tif"))
    print(f"找到 {len(dem_files)} 个流域DEM文件")
    
    # 处理年份范围（根据LC数据的实际波段对应关系）
    # 5years文件：1985, 1990, 1995 (3个波段)
    # Annual文件：2000-2022 (23个波段)
    years = [1985, 1990, 1995] + list(range(2000, 2023))  # 1985, 1990, 1995, 2000-2022
    
    # 准备参数列表
    args_list = []
    for dem_file in dem_files:
        for year in years:
            args_list.append((dem_file, year, lc_data_dir, output_dir, temp_dir))
    
    print(f"总共需要处理 {len(args_list)} 个任务")
    
    # 使用多进程处理
    num_workers = 2
    print(f"开始使用 {num_workers} 个进程并行处理...")
    
    with ProcessPoolExecutor(max_workers=num_workers) as executor:
        list(executor.map(process_single_basin, args_list))
    
    print("所有流域LC数据处理完成！")

if __name__ == "__main__":
    main()
