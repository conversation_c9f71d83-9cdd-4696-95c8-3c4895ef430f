import os
import numpy as np
import rasterio
from pathlib import Path
import h5py
import pandas as pd
import concurrent.futures
from functools import partial

def process_model_year(model_year_tuple, mask_data, m, n, basin_ids):
    """处理单个模型-年份组合的函数"""
    model, year = model_year_tuple
    print(f"  处理{model}模型{year}年数据...")

    # 读取mat文件 - 使用h5py方式，参照EPEs_0.25的读取方式
    mat_path = f'/mnt/sdb2/yuanshuai/wanpan/yuan/NASA/delta3/historical_jz_1971_2014/Pre_jz/{model}/CMIP6_Pre_jz_{year}.mat'

    with h5py.File(mat_path, 'r') as mat_data:
        pre_data = np.array(mat_data['data'])
        pre_data = pre_data.T  # 转置

    # 重塑数据，参照EPEs_0.25的处理方式
    pre_data_reshaped = pre_data.reshape(n, m, -1)  # 注意这里是n, m
    pre_data_reshaped = np.transpose(pre_data_reshaped, (1, 0, 2))  # (m, n, days)

    # 将数据拉平为 (m*n, days) 格式，与matlab的data_all对应
    data_all = pre_data_reshaped.reshape(m * n, -1)

    # 计算每个流域的逐日平均值
    basin_daily_means = np.zeros((len(basin_ids), 365))

    for b, basin_id in enumerate(basin_ids):
        # 获取当前流域的所有网格点
        basin_pixels = (mask_data == basin_id)

        # 计算该流域每天的均值
        for day in range(365):
            basin_daily_means[b, day] = np.nanmean(data_all[basin_pixels, day])

    # 准备CSV数据
    # 创建表头
    headers = ['BasinID'] + [f'Day_{day+1:03d}' for day in range(365)]

    # 创建数据矩阵
    csv_data = np.zeros((len(basin_ids), 366))  # 366列：1列BasinID + 365列数据
    csv_data[:, 0] = basin_ids  # 第一列是流域ID
    csv_data[:, 1:] = basin_daily_means  # 其余列是每日数据

    # 转换为DataFrame
    df = pd.DataFrame(csv_data, columns=headers)

    # 保存为CSV文件
    output_dir = f'/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03/pre_data/historical/basins_daily_mean_models/{model}'
    os.makedirs(output_dir, exist_ok=True)

    csv_filename = os.path.join(output_dir, f'basin_daily_means_{year}.csv')
    df.to_csv(csv_filename, index=False)

    print(f'  已完成{model}模式{year}年数据处理')
    return (model, year)

def main():
    # 文件路径设置
    geo_filename = '/mnt/sdb2/yuanshuai/wanpan/yuan/clip_biaozhun_tif/clip_tif_global_600_1440.tif'
    mask_geo_filename = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new02/shp/valid_pixels/updated_valid_pixels_basin_id.tif'

    # 读取地理信息和掩膜数据
    with rasterio.open(geo_filename) as src:
        m, n = src.height, src.width

    # 模型列表
    models = ['ACCESS-CM2', 'ACCESS-ESM1-5', 'BCC-CSM2-MR', 'CanESM5', 'CMCC-ESM2',
              'CNRM-CM6-1', 'CNRM-ESM2-1', 'EC-Earth3', 'EC-Earth3-Veg-LR', 'FGOALS-g3',
              'GFDL-ESM4', 'GISS-E2-1-G', 'INM-CM4-8', 'INM-CM5-0', 'IPSL-CM6A-LR',
              'MIROC6', 'MIROC-ES2L', 'MPI-ESM1-2-HR', 'MPI-ESM1-2-LR', 'MRI-ESM2-0',
              'NorESM2-LM', 'NorESM2-MM', 'TaiESM1']
    
    # 读入掩膜文件
    with rasterio.open(mask_geo_filename) as src:
        mask_data = src.read(1)

    # 将掩膜数据重塑为一维数组，与matlab中的reshape(mask_data,m*n,1)对应
    mask_data = mask_data.reshape(m * n)

    # 获取所有唯一的流域ID(排除0和NaN)
    basin_ids = np.unique(mask_data)
    basin_ids = basin_ids[basin_ids > 0]

    # 创建所有模型-年份组合
    years = list(range(1971, 2015))
    model_year_combinations = [(model, year) for model in models for year in years]

    print(f"开始使用多线程处理{len(models)}个模型，{len(years)}年的数据，共{len(model_year_combinations)}个任务...")

    # 创建部分函数，固定参数
    process_func = partial(process_model_year,
                          mask_data=mask_data,
                          m=m,
                          n=n,
                          basin_ids=basin_ids)

    # 使用线程池执行
    with concurrent.futures.ThreadPoolExecutor(max_workers=22) as executor:
        # 提交所有任务
        future_to_combination = {executor.submit(process_func, combination): combination
                                for combination in model_year_combinations}

        # 获取结果
        completed_tasks = []
        for future in concurrent.futures.as_completed(future_to_combination):
            combination = future_to_combination[future]
            try:
                result = future.result()
                completed_tasks.append(result)
                model, year = result
                print(f"线程完成: {model}模型{year}年")
            except Exception as exc:
                model, year = combination
                print(f'{model}模型{year}年处理出现异常: {exc}')

    print(f"所有任务处理完成，共处理了{len(completed_tasks)}个模型-年份组合")

if __name__ == "__main__":
    main()
