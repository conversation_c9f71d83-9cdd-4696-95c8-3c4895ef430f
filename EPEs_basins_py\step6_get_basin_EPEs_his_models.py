import os
import numpy as np
import pandas as pd

def compute_precip_indices(data_all, pre_yz):
    """
    计算极端降水指数
    data_all: (n_basins, days)  每个流域全年数据
    pre_yz: (n_basins, 3)       每个流域的90/95/99阈值
    返回: (n_basins, 10)         10个指标
    """
    n, days = data_all.shape
    outdata = np.full((n, 10), np.nan, dtype=np.float32)
    
    for i in range(n):
        row = data_all[i, :]
        yz90, yz95, yz99 = pre_yz[i, :]
        
        # R90pD, R90pTOT
        if np.isnan(yz90):
            R90pD = np.nan
            R90pTOT = np.nan
        else:
            mask90 = row > yz90
            R90pD = np.sum(mask90)
            R90pTOT = np.sum(row[mask90])
        
        # R95pD, R95pTOT
        if np.isnan(yz95):
            R95pD = np.nan
            R95pTOT = np.nan
        else:
            mask95 = row > yz95
            R95pD = np.sum(mask95)
            R95pTOT = np.sum(row[mask95])
        
        # R99pD, R99pTOT
        if np.isnan(yz99):
            R99pD = np.nan
            R99pTOT = np.nan
        else:
            mask99 = row > yz99
            R99pD = np.sum(mask99)
            R99pTOT = np.sum(row[mask99])
        
        # RX1-day
        RX1_day = np.nanmax(row)
        
        # R90pI, R95pI, R99pI (强度指标)
        R90pI = R90pTOT / R90pD if R90pD > 0 else np.nan
        R95pI = R95pTOT / R95pD if R95pD > 0 else np.nan
        R99pI = R99pTOT / R99pD if R99pD > 0 else np.nan
        
        # 组合结果
        outdata[i, 0] = R90pD
        outdata[i, 1] = R90pTOT
        outdata[i, 2] = R95pD
        outdata[i, 3] = R95pTOT
        outdata[i, 4] = R99pD
        outdata[i, 5] = R99pTOT
        outdata[i, 6] = R90pI
        outdata[i, 7] = R95pI
        outdata[i, 8] = R99pI
        outdata[i, 9] = RX1_day
    
    return outdata

def main():
    # 极端降水指数名称
    HW_indexs = ['R90pD', 'R90pTOT', 'R95pD', 'R95pTOT', 'R99pD', 'R99pTOT', 'R90pI', 'R95pI', 'R99pI', 'RX1-day']
    
    # 定义模型
    models = ['ACCESS-CM2', 'ACCESS-ESM1-5', 'BCC-CSM2-MR', 'CanESM5', 'CMCC-ESM2', 
              'CNRM-CM6-1', 'CNRM-ESM2-1', 'EC-Earth3', 'EC-Earth3-Veg-LR', 'FGOALS-g3', 
              'GFDL-ESM4', 'GISS-E2-1-G', 'INM-CM4-8', 'INM-CM5-0', 'IPSL-CM6A-LR', 
              'MIROC6', 'MIROC-ES2L', 'MPI-ESM1-2-HR', 'MPI-ESM1-2-LR', 'MRI-ESM2-0',
              'NorESM2-LM', 'NorESM2-MM', 'TaiESM1']
    
    # 设置输入和输出目录
    yz_dir = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03/pre_data/historical/basins_yz'
    
    # 读取阈值数据
    yz_file = os.path.join(yz_dir, 'basin_precipitation_thresholds2.csv')
    yz_table = pd.read_csv(yz_file)
    
    # 获取阈值数据
    pre_yz = yz_table[['P90', 'P95', 'P99']].values
    
    print('已读取流域降水阈值数据')
    
    # 循环处理每个模型
    for model in models:
        print(f"处理模型: {model}")
        
        # 设置输入输出路径
        input_dir = f'/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03/pre_data/historical/basins_daily_mean_models/{model}'
        output_dir = f'/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03/pre_data/historical/basins_EPEs_models/{model}'
        
        # 如果输出目录不存在则创建
        os.makedirs(output_dir, exist_ok=True)
        
        # 初始化存储流域ID
        basin_ids = None
        
        # 循环处理每一年的数据
        for year in range(1971, 2015):
            print(f"  处理{year}年数据...")
            
            # 构建输入CSV文件路径
            csv_filename = os.path.join(input_dir, f'basin_daily_means_{year}.csv')
            
            # 读取CSV文件
            df = pd.read_csv(csv_filename)
            
            # 获取流域ID（只在第一年获取）
            if year == 1971:
                basin_ids = df['BasinID'].values
            
            # 获取日降水数据(不包括BasinID列)
            daily_data = df.iloc[:, 1:].values  # 跳过第一列BasinID
            
            # 计算极端降水指数
            outdata = compute_precip_indices(daily_data, pre_yz)
            
            # 创建表头
            headers = ['R90pD', 'R90pTOT', 'R95pD', 'R95pTOT', 'R99pD', 'R99pTOT', 'R90pI', 'R95pI', 'R99pI', 'RX1-day']
            
            # 将数据转换为DataFrame
            result_df = pd.DataFrame(outdata, columns=headers)
            
            # 构建输出文件路径
            output_file = os.path.join(output_dir, f'EPEs_{year}.csv')
            
            # 将DataFrame写入CSV文件
            result_df.to_csv(output_file, index=False)
            
            print(f'  已完成{model}模式{year}年数据处理')

if __name__ == "__main__":
    main()
