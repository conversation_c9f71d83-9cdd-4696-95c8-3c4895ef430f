import os
import numpy as np
import pandas as pd

def main():
    # 极端降水指数名称
    HW_indexs = ['R90pD', 'R90pTOT', 'R95pD', 'R95pTOT', 'R99pD', 'R99pTOT', 'R90pI', 'R95pI', 'R99pI', 'RX1-day']

    # 设置输入和输出目录
    input_dir = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03/pre_data/historical/basins_EPEs'

    # 初始化存储所有年份数据的列表
    all_years_data = []

    # 循环读取每一年的数据
    for year in range(1971, 2021):
        print(f"读取{year}年历史极端降水数据...")

        # 构建输入文件路径
        input_file = os.path.join(input_dir, f'EPEs_{year}.csv')

        # 读取CSV文件
        data = pd.read_csv(input_file)

        # 将数据转换为数组
        year_data = data.values

        # 将年度数据添加到列表中
        all_years_data.append(year_data)

        print(f'已读取{year}年历史极端降水数据')

    # 将列表转换为numpy数组 (years, basins, indices)
    all_years_data = np.array(all_years_data)  # (50, n_basins, 10)

    # 计算多年平均值 - 沿着年份维度（axis=0）取平均
    mean_data = np.mean(all_years_data, axis=0)  # (n_basins, 10)

    # 读取1971年的流域ID
    basin_file = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03/pre_data/historical/basins_daily_mean/basin_daily_means_1971.csv'
    basin_data = pd.read_csv(basin_file)
    basin_ids = basin_data['BasinID'].values

    # 将流域ID和平均值数据合并
    data_with_ids = np.column_stack([basin_ids, mean_data])

    # 创建列名
    column_names = ['BasinID'] + HW_indexs

    # 转换为DataFrame并添加列名
    result_df = pd.DataFrame(data_with_ids, columns=column_names)

    # 构建输出文件路径
    output_file = os.path.join(input_dir, 'EPEs_historical_basin_mean.csv')

    # 保存为CSV文件
    result_df.to_csv(output_file, index=False)

    print('已完成历史极端降水指标多年平均值的计算和保存')
    print(f'结果已保存至: {output_file}')

if __name__ == "__main__":
    main()