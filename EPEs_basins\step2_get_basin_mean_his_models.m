clc;clear;close all

addpath '/mnt/sdb2/yuanshuai/code'

geo_filename ='/mnt/sdb2/yuanshuai/wanpan/yuan/clip_biaozhun_tif/clip_tif_global_600_1440.tif';
mask_geo_filename ='/mnt/sdb2/yuanshuai/wanpan/yuan/clip_biaozhun_tif/clip_tif_basin4_600_1440.tif';

[a,R]=geotiffread(geo_filename);%先导入投影信息，某个影像的路径就行（最好是你分析的数据中的一个）
info=geotiffinfo(geo_filename);%同上
[m,n]=size(a);

models = {'ACCESS-CM2', 'ACCESS-ESM1-5', 'BCC-CSM2-MR', 'CanESM5', 'CMCC-ESM2', 'CNRM-CM6-1', 'CNRM-ESM2-1', 'EC-Earth3', 'EC-Earth3-Veg-LR', 'FGOALS-g3', 'GFDL-ESM4', 'GISS-E2-1-G', 'INM-CM4-8', 'INM-CM5-0', 'IPSL-CM6A-LR', 'MIROC6', 'MIROC-ES2L', 'MPI-ESM1-2-HR', 'MPI-ESM1-2-LR', 'MRI-ESM2-0','NorESM2-LM','NorESM2-MM','TaiESM1'};

first_day_of_mon = [1 32 60 91 121 152 182 213 244 274 305 335];
end_day_of_mon = [31 59 90 120 151 181 212 243 273 304 334 365]; 

% 读入掩膜文件
[mask_data,~] = geotiffread(mask_geo_filename);
mask_data = reshape(mask_data,m*n,1);
% 获取所有唯一的流域ID(排除0和NaN)
basin_ids = unique(mask_data);
basin_ids = basin_ids(basin_ids > 0);

% 循环处理每个模型
for model_i = 1:length(models)
    model = models{model_i};
    
    % 创建输出文件夹
    output_dir = sprintf('/mnt/sdb2/yuanshuai/wanpan/yuan/paper3/pre_data/historical/basins_daily_mean_models/%s', model);
    if ~exist(output_dir, 'dir')
        mkdir(output_dir);
    end
    
    % 循环处理每一年
    for y = 1971:2014
        y_str = num2str(y);
        
        % 读取mat文件
        mat_path = sprintf('/mnt/sdb2/yuanshuai/wanpan/yuan/NASA/delta3/historical_jz_1971_2014/Pre_jz/%s/CMIP6_Pre_jz_%s.mat', model, y_str);
        mat_data = load(mat_path);
        data_all = mat_data.data;
        
        % 计算每个流域的逐日平均值
        basin_daily_means = zeros(length(basin_ids), 365);
        for b = 1:length(basin_ids)
            basin_id = basin_ids(b);
            % 获取当前流域的所有网格点
            basin_pixels = mask_data == basin_id;
            % 计算该流域每天的均值
            for day = 1:365
                basin_daily_means(b, day) = mean(data_all(basin_pixels, day), 'omitnan');
            end
        end
        
        % 准备CSV数据
        % 创建表头
        headers = {'BasinID'};
        for day = 1:365
            headers = [headers, {sprintf('Day_%03d', day)}];
        end
        
        % 创建数据矩阵
        csv_data = zeros(length(basin_ids), 366);  % 366列：1列BasinID + 365列数据
        csv_data(:,1) = basin_ids;  % 第一列是流域ID
        csv_data(:,2:end) = basin_daily_means;  % 其余列是每日数据
        
        % 转换为表格
        T = array2table(csv_data, 'VariableNames', headers);
        
        % 保存为CSV文件
        csv_filename = fullfile(output_dir, sprintf('basin_daily_means_%s.csv', y_str));
        writetable(T, csv_filename);
        fprintf('已完成%s模式%d年数据处理\n', model, y);
    end
end
