import os
import numpy as np
import rasterio
from pathlib import Path
import h5py
import pandas as pd
import concurrent.futures
from functools import partial

def process_model_ssp_year(task_tuple, mask_data, m, n, basin_ids):
    """处理单个模型-情景-年份组合的函数"""
    model, ssp, year = task_tuple
    print(f"  处理{model}模型{ssp}情景{year}年数据...")

    # 读取mat文件 - 使用h5py方式，参照EPEs_0.25的读取方式
    mat_path = f'/mnt/sdb2/yuanshuai/wanpan/yuan/NASA/delta3/ssp_jz_2021_2100/Pre_jz/{ssp}/{model}/CMIP6_Pre_jz_{year}.mat'

    with h5py.File(mat_path, 'r') as mat_data:
        pre_data = np.array(mat_data['data'])
        pre_data = pre_data.T  # 转置

    # 重塑数据，参照EPEs_0.25的处理方式
    pre_data_reshaped = pre_data.reshape(n, m, -1)  # 注意这里是n, m
    pre_data_reshaped = np.transpose(pre_data_reshaped, (1, 0, 2))  # (m, n, days)

    # 将数据拉平为 (m*n, days) 格式，与matlab的data_all对应
    data_all = pre_data_reshaped.reshape(m * n, -1)

    # 计算每个流域的逐日平均值
    basin_daily_means = np.zeros((len(basin_ids), 365))

    for b, basin_id in enumerate(basin_ids):
        # 获取当前流域的所有网格点
        basin_pixels = (mask_data == basin_id)

        # 计算该流域每天的均值
        for day in range(365):
            basin_daily_means[b, day] = np.nanmean(data_all[basin_pixels, day])

    # 准备CSV数据
    # 创建表头
    headers = ['BasinID'] + [f'Day_{day+1:03d}' for day in range(365)]

    # 创建数据矩阵
    csv_data = np.zeros((len(basin_ids), 366))  # 366列：1列BasinID + 365列数据
    csv_data[:, 0] = basin_ids  # 第一列是流域ID
    csv_data[:, 1:] = basin_daily_means  # 其余列是每日数据

    # 转换为DataFrame
    df = pd.DataFrame(csv_data, columns=headers)

    # 保存为CSV文件
    output_dir = f'/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03/pre_data/ssp/basins_daily_mean/{ssp}/{model}'
    os.makedirs(output_dir, exist_ok=True)

    csv_filename = os.path.join(output_dir, f'basin_daily_means_{year}.csv')
    df.to_csv(csv_filename, index=False)

    print(f'  已完成{model}_{ssp}_{year}年数据处理')
    return (model, ssp, year)

def main():
    # 文件路径设置
    geo_filename = '/mnt/sdb2/yuanshuai/wanpan/yuan/clip_biaozhun_tif/clip_tif_global_600_1440.tif'
    mask_geo_filename = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new02/shp/valid_pixels/updated_valid_pixels_basin_id.tif'
    
    # 读取地理信息和掩膜数据
    with rasterio.open(geo_filename) as src:
        profile = src.profile
        m, n = src.height, src.width
    
    # 定义模型和情景
    models = ['ACCESS-CM2', 'ACCESS-ESM1-5', 'BCC-CSM2-MR', 'CanESM5', 'CMCC-ESM2', 
              'CNRM-CM6-1', 'CNRM-ESM2-1', 'EC-Earth3', 'EC-Earth3-Veg-LR', 'FGOALS-g3', 
              'GFDL-ESM4', 'GISS-E2-1-G', 'INM-CM4-8', 'INM-CM5-0', 'IPSL-CM6A-LR', 
              'MIROC6', 'MIROC-ES2L', 'MPI-ESM1-2-HR', 'MPI-ESM1-2-LR', 'MRI-ESM2-0',
              'NorESM2-LM', 'NorESM2-MM', 'TaiESM1']
    ssps = ['ssp126', 'ssp245', 'ssp370', 'ssp585']
    
    # 读入掩膜文件
    with rasterio.open(mask_geo_filename) as src:
        mask_data = src.read(1)

    # 将掩膜数据重塑为一维数组，与matlab中的reshape(mask_data,m*n,1)对应
    mask_data = mask_data.reshape(m * n)

    # 获取所有唯一的流域ID(排除0和NaN)
    basin_ids = np.unique(mask_data)
    basin_ids = basin_ids[basin_ids > 0]

    # 创建所有模型-情景-年份组合
    years = list(range(2021, 2101))
    task_combinations = [(model, ssp, year) for model in models for ssp in ssps for year in years]

    print(f"第一部分：开始使用多线程处理{len(models)}个模型，{len(ssps)}个情景，{len(years)}年的数据，共{len(task_combinations)}个任务...")

    # 创建部分函数，固定参数
    process_func = partial(process_model_ssp_year,
                          mask_data=mask_data,
                          m=m,
                          n=n,
                          basin_ids=basin_ids)

    # 使用线程池执行
    with concurrent.futures.ThreadPoolExecutor(max_workers=20) as executor:
        # 提交所有任务
        future_to_combination = {executor.submit(process_func, combination): combination
                                for combination in task_combinations}

        # 获取结果
        completed_tasks = []
        for future in concurrent.futures.as_completed(future_to_combination):
            combination = future_to_combination[future]
            try:
                result = future.result()
                completed_tasks.append(result)
                model, ssp, year = result
                print(f"线程完成: {model}_{ssp}_{year}")
            except Exception as exc:
                model, ssp, year = combination
                print(f'{model}_{ssp}_{year}处理出现异常: {exc}')

    print(f"第一部分完成，共处理了{len(completed_tasks)}个模型-情景-年份组合")
    
    # 第二部分：计算多模式集合平均值
    print("\n开始计算多模式集合平均值...")
    
    # 循环处理每个SSP情景
    for ssp in ssps:
        print(f"处理SSP情景: {ssp}")
        
        # 循环处理每一年
        for year in range(2021, 2101):
            print(f"  处理{year}年数据...")
            
            # 初始化存储所有模式数据的矩阵
            all_models_data = []
            
            # 读取每个模式的数据
            for model in models:
                csv_path = f'/mnt/sdb2/yuanshuai/wanpan/yuan/paper3/pre_data/ssp/basins_daily_mean/{ssp}/{model}/basin_daily_means_{year}.csv'
                
                # 读取CSV文件
                data = pd.read_csv(csv_path)
                
                # 转换为数组并去除BasinID列
                model_data = data.iloc[:, 1:].values
                
                # 将数据添加到列表中
                all_models_data.append(model_data)
            
            # 转换为numpy数组并计算多模式集合平均值
            all_models_data = np.array(all_models_data)  # (models, basins, days)
            mme_data = np.mean(all_models_data, axis=0)  # (basins, days)
            
            # 准备CSV数据
            # 创建表头
            headers = ['BasinID'] + [f'Day_{day+1:03d}' for day in range(365)]
            
            # 创建数据矩阵
            basin_ids_from_data = data['BasinID'].values  # 从最后一个读取的数据中获取流域ID
            csv_data = np.zeros((len(basin_ids_from_data), 366))  # 366列：1列BasinID + 365列数据
            csv_data[:, 0] = basin_ids_from_data  # 第一列是流域ID
            csv_data[:, 1:] = mme_data  # 其余列是MME数据
            
            # 转换为DataFrame
            df = pd.DataFrame(csv_data, columns=headers)
            
            # 保存为CSV文件
            output_dir = f'/mnt/sdb2/yuanshuai/wanpan/yuan/paper3/pre_data/ssp/basins_daily_mean/{ssp}/MME'
            os.makedirs(output_dir, exist_ok=True)
            
            csv_filename = os.path.join(output_dir, f'basin_daily_means_{year}.csv')
            df.to_csv(csv_filename, index=False)
            
            print(f'  已完成{ssp}_MME_{year}年数据处理')

if __name__ == "__main__":
    main()
