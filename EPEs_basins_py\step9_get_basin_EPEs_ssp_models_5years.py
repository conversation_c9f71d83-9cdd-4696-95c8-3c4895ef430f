import os
import numpy as np
import pandas as pd

def main():
    # 极端降水指数名称
    HW_indexs = ['R90pD', 'R90pTOT', 'R95pD', 'R95pTOT', 'R99pD', 'R99pTOT', 'R90pI', 'R95pI', 'R99pI', 'RX1-day']
    
    # 定义SSP情景和年份范围
    ssps = ['ssp126', 'ssp245', 'ssp370', 'ssp585']
    models = ['ACCESS-CM2', 'ACCESS-ESM1-5', 'BCC-CSM2-MR', 'CanESM5', 'CMCC-ESM2', 
              'CNRM-CM6-1', 'CNRM-ESM2-1', 'EC-Earth3', 'EC-Earth3-Veg-LR', 'FGOALS-g3', 
              'GFDL-ESM4', 'GISS-E2-1-G', 'INM-CM4-8', 'INM-CM5-0', 'IPSL-CM6A-LR', 
              'MIROC6', 'MIROC-ES2L', 'MPI-ESM1-2-HR', 'MPI-ESM1-2-LR', 'MRI-ESM2-0',
              'NorESM2-LM', 'NorESM2-MM', 'TaiESM1']
    
    # 设置输入和输出目录
    input_dir = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03/pre_data/ssp/basins_EPEs_corrected'
    output_dir = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03/pre_data/ssp/basins_EPEs_corrected_5years2'
    
    # 读取1971年的流域ID
    basin_file = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03/pre_data/historical/basins_daily_mean/basin_daily_means_1971.csv'
    basin_data = pd.read_csv(basin_file)
    basin_ids = basin_data['BasinID'].values
    
    # 循环处理每个SSP情景
    for ssp in ssps:
        print(f"处理SSP情景: {ssp}")
        
        # 循环处理每个模型
        for model in models:
            print(f"  处理模型: {model}")
            
            # 构建模型输入目录
            model_input_dir = os.path.join(input_dir, ssp, model)
            
            # 初始化存储所有年份数据的列表
            all_years_data = []
            
            # 循环读取每一年的数据
            for year in range(2021, 2101):
                # 构建输入文件路径
                input_file = os.path.join(model_input_dir, f'EPEs_{year}.csv')
                
                # 读取CSV文件
                data = pd.read_csv(input_file)
                
                # 将数据转换为数组
                year_data = data.values
                
                # 将年度数据添加到列表中
                all_years_data.append(year_data)
                
                print(f'    已读取{model}_{ssp}_{year}年校正后的未来极端降水数据')
            
            # 将列表转换为numpy数组 (years, basins, indices)
            all_years_data = np.array(all_years_data)  # (80, basins, 10)
            
            # 计算5年平均值
            for i in range(16):  # 16个5年段(2021-2025, 2026-2030, ..., 2096-2100)
                start_idx = i * 5
                end_idx = (i + 1) * 5
                end_year = 2020 + end_idx
                
                # 计算5年平均值
                five_year_mean = np.nanmean(all_years_data[start_idx:end_idx, :, :], axis=0)
                
                # 创建输出目录
                model_output_dir = os.path.join(output_dir, ssp, model)
                os.makedirs(model_output_dir, exist_ok=True)
                
                # 将basin_ids添加到five_year_mean的第一列
                data_with_ids = np.column_stack([basin_ids, five_year_mean])
                
                # 创建列名(包含BasinID)
                column_names = ['BasinID'] + HW_indexs
                
                # 保存5年平均值
                df = pd.DataFrame(data_with_ids, columns=column_names)
                output_file = os.path.join(model_output_dir, f'EPEs_{end_year}.csv')
                df.to_csv(output_file, index=False)
                
                print(f'    已保存{model}_{ssp}_{end_year}年5年平均值数据')

if __name__ == "__main__":
    main()
