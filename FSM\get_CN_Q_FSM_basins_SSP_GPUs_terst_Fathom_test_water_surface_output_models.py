from osgeo import gdal
import os
import numpy as np
import pandas as pd
import subprocess
from concurrent.futures import ProcessPoolExecutor
import rasterio

def create_cn_lookup_table():
    """
    创建CN值查找表
    返回一个嵌套字典，格式为：{LC: {soil_group: cn_value}}
    soil_group: 1-4 对应 A-D组
    """
    cn_table = {
        1: {1: 34, 2: 60, 3: 73, 4: 79},   # LC类型1
        2: {1: 30, 2: 58, 3: 71, 4: 78},
        3: {1: 40, 2: 64, 3: 77, 4: 83},
        4: {1: 42, 2: 66, 3: 79, 4: 85},
        5: {1: 38, 2: 61, 3: 75, 4: 81},
        6: {1: 45, 2: 65, 3: 75, 4: 80},
        7: {1: 49, 2: 69, 3: 79, 4: 84},
        8: {1: 61, 2: 71, 3: 81, 4: 89},
        9: {1: 72, 2: 80, 3: 87, 4: 93},
        10: {1: 49, 2: 69, 3: 79, 4: 84},
        11: {1: 30, 2: 58, 3: 71, 4: 78},
        12: {1: 67, 2: 78, 3: 85, 4: 89},
        13: {1: 72, 2: 82, 3: 90, 4: 95},
        14: {1: 52, 2: 69, 3: 79, 4: 84},
        16: {1: 72, 2: 82, 3: 83, 4: 87}
    }
    return cn_table

def get_cn_value(lc_type, soil_group):
    """
    根据土地利用类型和土壤组获取CN值
    
    参数:
    lc_type: 土地利用类型编号
    soil_group: 土壤组编号(1-4)
    
    返回:
    cn_value: 对应的CN值，如果没有找到则返回None
    """
    cn_table = create_cn_lookup_table()
    try:
        return cn_table[lc_type][soil_group]
    except KeyError:
        return None

def calculate_Q(P, CN):
    """
    计算径流深度Q
    
    参数:
    P: 降水量(毫米)
    CN: CN值数组
    
    返回:
    Q: 径流深度数组(米)
    """
    # 将降水量从毫米转换为米
    P = P / 1000.0
    
    # 创建输出数组
    Q = np.zeros_like(CN, dtype=np.float32)
    
    # 只在CN > 0的地方计算S和Q
    valid_mask = CN > 0
    
    if np.any(valid_mask):
        # 计算S值(最大潜在滞留量,单位:米)
        S = np.zeros_like(CN, dtype=np.float32)
        S[valid_mask] = (25400/CN[valid_mask] - 254) / 1000.0
        
        # 计算Q值
        # 注意：当P <= 0.2S时，Q = 0
        runoff_mask = (P > 0.2*S) & valid_mask
        Q[runoff_mask] = ((P - 0.2*S[runoff_mask])**2) / (P + 0.8*S[runoff_mask])
    
    return Q

def calculate_cn_values(lc_data, soil_data):
    """
    根据土地利用类型和土壤组数据计算CN值
    
    参数:
    lc_data: 土地利用类型数据数组
    soil_data: 土壤组数据数组
    
    返回:
    cn_data: CN值数组
    """
    rows, cols = lc_data.shape
    cn_data = np.zeros((rows, cols), dtype=np.float32)
    
    for i in range(rows):
        for j in range(cols):
            lc_type = lc_data[i, j]
            soil_group = soil_data[i, j]
            if lc_type > 0 and soil_group > 0:  # 确保数据有效
                cn_value = get_cn_value(lc_type, soil_group)
                if cn_value is not None:
                    cn_data[i, j] = cn_value
                else:
                    cn_data[i, j] = 0  # 无效值标记为0
            else:
                cn_data[i, j] = 0  # 无效值标记为0
    
    return cn_data

def align_raster_coordinates(reference_raster_path, target_raster_paths):
    """
    使用参考栅格的地理参考信息来更新目标栅格的坐标系统
    
    参数:
    reference_raster_path: str, 参考栅格文件路径（比如DEM文件）
    target_raster_paths: list, 需要更新的目标栅格文件路径列表
    """
    # 打开参考栅格并获取其地理信息
    try:
        ref_ds = gdal.Open(reference_raster_path)
        if ref_ds is None:
            print(f"错误：无法打开参考栅格文件 {reference_raster_path}")
            return
        
        # 获取参考栅格的地理变换参数和投影信息
        ref_geotransform = ref_ds.GetGeoTransform()
        ref_projection = ref_ds.GetProjection()
        
        # 关闭参考数据集
        ref_ds = None
        
        # 处理每个目标栅格
        for target_path in target_raster_paths:
            try:
                print(f"正在投影: {target_path}")
                
                # 打开目标栅格用于更新
                target_ds = gdal.Open(target_path, gdal.GA_Update)
                if target_ds is None:
                    print(f"错误：无法打开目标栅格文件 {target_path}")
                    continue
                
                # 更新目标栅格的地理信息
                target_ds.SetGeoTransform(ref_geotransform)
                target_ds.SetProjection(ref_projection)
                
                # 关闭目标数据集以保存更改
                target_ds = None
                print(f"成功更新: {target_path}")
                
            except Exception as e:
                print(f"处理 {target_path} 时发生错误: {str(e)}")
                continue
                
    except Exception as e:
        print(f"处理参考栅格时发生错误: {str(e)}")

def apply_surface_water_mask(dem_file, target_raster, base_dir):
    """
    使用地表水掩膜对目标栅格进行掩膜处理，并使用DEFLATE压缩保存结果
    
    参数:
    dem_file: str, DEM文件路径
    target_raster: str, 需要掩膜的目标栅格路径
    output_dir: str, 输出目录路径
    """
    import os
    import rasterio
    
    try:
        # 从DEM文件路径提取流域ID
        basin_id = os.path.basename(dem_file).split('_')[1]
        
        # 构建地表水掩膜文件路径
        surface_water_dir = r"/mnt/sdb2/yuanshuai/wanpan/yuan/paper3/FSM/surface_water/basins_lev05"
        mask_file = os.path.join(surface_water_dir, f"basin_{basin_id}_surface_water.tif")
        
        if not os.path.exists(mask_file):
            print(f"掩膜文件不存在: {mask_file}")
            return
            
        # 打开掩膜文件和目标栅格
        with rasterio.open(mask_file) as mask_ds, rasterio.open(target_raster) as target_ds:
            # 读取数据
            mask_data = mask_ds.read(1)
            target_data = target_ds.read(1)
            
            # 应用掩膜
            target_data[mask_data == 0] = 0
            
            # 获取原始文件的配置信息
            profile = target_ds.profile.copy()
            
            # 更新配置，添加DEFLATE压缩
            profile.update({
                'compress': 'DEFLATE',
                'zlevel': 9,  # 最高压缩级别
                'predictor': 2  # 浮点数据使用predictor 2
            })
        
        # 确保输出目录存在
        if not os.path.exists(base_dir):
            os.makedirs(base_dir)
            
        # 构建输出文件路径
        output_filename = os.path.basename(target_raster)
        output_path = os.path.join(base_dir, output_filename)
        
        # 将处理后的数据写入新文件
        with rasterio.open(output_path, 'w', **profile) as dst:
            dst.write(target_data, 1)
            
        # 删除原始文件
        if os.path.exists(target_raster):
            os.remove(target_raster)
            
        print(f"成功应用掩膜并保存到: {output_path}")
        
    except Exception as e:
        print(f"应用掩膜时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()


def run_fsm_command(dem_file, q_file, base_dir, output_dir):
    """执行单个FSM命令"""
    # 从DEM文件路径提取流域ID
    basin_id = os.path.basename(dem_file).split('_')[1]
    
    # 构建输出目录
    if not os.path.exists(base_dir):
        os.makedirs(base_dir)
    output_dir_fsm = f"{output_dir}/basin_{basin_id}"
    
    # 构建完整的命令
    cmd = [
        "/mnt/sdb2/yuanshuai/wanpan/yuan/gooleEeath/env/Barnes2020-FillSpillMerge/build/fsm.exe",
        dem_file,
        output_dir_fsm,
        f"--swf={q_file}",
        "0"
    ]
    
    try:
        subprocess.run(cmd, check=True)
        print(f"成功处理: {basin_id}")
    except subprocess.CalledProcessError as e:
        print(f"处理失败 {basin_id}: {str(e)}")

    # 删除hydrologic-surface-height.tif文件
    hydrologic_file = f"{output_dir_fsm}-hydrologic-surface-height.tif"
    if os.path.exists(hydrologic_file):
        os.remove(hydrologic_file)

    # 生成目标栅格文件路径列表
    target_rasters = [
        f"{output_dir_fsm}-wtd.tif"
    ]
    align_raster_coordinates(dem_file, target_rasters)

    # 应用地表水掩膜
    for target_raster in target_rasters:
        apply_surface_water_mask(dem_file, target_raster, base_dir)

def process_single_file(args):
    """
    处理单个文件的Q值计算和FSM运行
    
    参数:
    args: 包含所有必要参数的元组 (tif_file, input_dir, output_dir, lc_path, soil_path, pre_data, base_dir)
    """
    tif_file, input_dir, output_dir, lc_path, soil_path, pre_data, base_dir = args
    
    try:
        # 在每个进程中重新打开数据集
        lc_dataset = gdal.Open(lc_path)
        soil_dataset = gdal.Open(soil_path)
        
        if lc_dataset is None or soil_dataset is None:
            print(f"无法打开数据文件: {tif_file}")
            return
        
        # 读取clip数据
        clip_path = os.path.join(input_dir, tif_file)
        clip_dataset = gdal.Open(clip_path)
        if clip_dataset is None:
            print(f"无法打开clip文件: {tif_file}")
            return
            
        # 获取clip的行列数和地理信息
        clip_cols = clip_dataset.RasterXSize
        clip_rows = clip_dataset.RasterYSize
        clip_transform = clip_dataset.GetGeoTransform()
        clip_proj = clip_dataset.GetProjection()
        
        # 创建输出文件名
        basin_id = tif_file.split('_')[1].split('.')[0]
        
        # 获取DEM文件路径
        dem_file = f"/mnt/sdb2/yuanshuai/wanpan/yuan/paper3/FSM/basins_DEM_lev05/Fathom/basin_{basin_id}_DEM.tif"
        
        
        # 获取降水数据
        pre_value = pre_data.loc[pre_data['BasinID'] == int(basin_id), 'RX1-day'].values[0]
        # print(f"流域 {basin_id} 的降水值: {pre_value}")
        
        # 获取clip的边界范围
        minx = clip_transform[0]
        maxy = clip_transform[3]
        maxx = minx + clip_transform[1] * clip_cols
        miny = maxy + clip_transform[5] * clip_rows
        
        # 1. 裁剪LC和soil数据到流域范围
        temp_lc_clip = os.path.join(output_dir, f'temp_lc_clip_{basin_id}.tif')
        temp_soil_clip = os.path.join(output_dir, f'temp_soil_clip_{basin_id}.tif')
        
        # 裁剪LC数据
        gdal.Warp(temp_lc_clip,
                  lc_dataset,
                  format='GTiff',
                  outputBounds=[minx, miny, maxx, maxy],
                  srcSRS=lc_dataset.GetProjection(),
                  dstSRS=clip_proj)
        
        # 裁剪soil数据
        gdal.Warp(temp_soil_clip,
                  soil_dataset,
                  format='GTiff',
                  outputBounds=[minx, miny, maxx, maxy],
                  srcSRS=soil_dataset.GetProjection(),
                  dstSRS=clip_proj)
        
        # 2. 读取裁剪后的soil数据以获取其分辨率
        soil_clip_ds = gdal.Open(temp_soil_clip)
        if soil_clip_ds is None:
            print(f"无法打开裁剪后的soil文件: {basin_id}")
            return
            
        soil_cols = soil_clip_ds.RasterXSize
        soil_rows = soil_clip_ds.RasterYSize
        soil_clip_ds = None
        
        # 3. 将裁剪后的LC重采样到soil的分辨率
        temp_lc_resample = os.path.join(output_dir, f'temp_lc_resample_{basin_id}.tif')
        gdal.Warp(temp_lc_resample,
                  temp_lc_clip,
                  format='GTiff',
                  width=soil_cols,
                  height=soil_rows,
                  resampleAlg=gdal.GRA_NearestNeighbour)
        
        # 4. 在soil分辨率下计算CN值和Q值
        lc_ds = gdal.Open(temp_lc_resample)
        soil_ds = gdal.Open(temp_soil_clip)
        
        if lc_ds is None or soil_ds is None:
            print(f"无法打开重采样后的LC或soil文件: {basin_id}")
            return
        
        lc_data = lc_ds.ReadAsArray()
        soil_data = soil_ds.ReadAsArray()
        
        # 计算CN值和Q值
        cn_data = calculate_cn_values(lc_data, soil_data)
        q_data = calculate_Q(pre_value, cn_data)
        
        # 保存低分辨率的Q值
        temp_q = os.path.join(output_dir, f'temp_q_{basin_id}.tif')
        
        # 保存Q值
        driver = gdal.GetDriverByName('GTiff')
        temp_q_ds = driver.Create(temp_q, 
                                soil_cols,
                                soil_rows,
                                1,
                                gdal.GDT_Float32)
        
        # 设置地理信息
        temp_q_ds.SetGeoTransform(soil_ds.GetGeoTransform())
        temp_q_ds.SetProjection(soil_ds.GetProjection())
        
        # 写入Q数据
        temp_band = temp_q_ds.GetRasterBand(1)
        temp_band.SetNoDataValue(0)
        temp_band.WriteArray(q_data)
        
        # 关闭临时数据集
        lc_ds = None
        soil_ds = None
        temp_q_ds = None
        
        # 读取clip数据作为掩膜
        clip_data = clip_dataset.ReadAsArray()
        
        # 5. 将Q值重采样到目标分辨率并应用掩膜
        q_output = os.path.join(output_dir, f'basin_{basin_id}_Q.tif')
        gdal.Warp(q_output,
                  temp_q,
                  format='GTiff',
                  width=clip_cols,
                  height=clip_rows,
                  resampleAlg=gdal.GRA_NearestNeighbour)
        
        # 应用掩膜到Q
        q_final_ds = gdal.Open(q_output, gdal.GA_Update)
        q_final_data = q_final_ds.ReadAsArray()
        q_final_data[clip_data == 0] = 0
        q_band = q_final_ds.GetRasterBand(1)
        q_band.SetNoDataValue(0)
        q_band.WriteArray(q_final_data)
        q_final_ds = None

        # 6. 运行FSM
        run_fsm_command(dem_file, q_output, base_dir, output_dir)
        
        # 7. 清理临时文件和Q值结果
        for temp_file in [temp_lc_clip, temp_soil_clip, temp_lc_resample, temp_q, q_output]:
            if os.path.exists(temp_file):
                os.remove(temp_file)
        
        print(f"已完成 {tif_file} 的FSM处理")
        
    except Exception as e:
        print(f"处理 {tif_file} 时出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 确保关闭数据集
        try:
            clip_dataset = None
            lc_dataset = None
            soil_dataset = None
        except:
            pass

def process_q_values(input_dir, output_dir, lc_path, soil_path, pre_path, base_dir, num_workers=4):
    """
    处理流域的Q值计算和FSM运行
    """
    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 获取所有DEM tif文件
    tif_files = [f for f in os.listdir(input_dir) if f.endswith('.tif')]
    print(f"找到 {len(tif_files)} 个文件需要处理")

    # 读取降水数据
    pre_data = pd.read_csv(pre_path)
    print("降水数据形状:", pre_data.shape)

    # 准备参数列表
    args_list = [(tif_file, input_dir, output_dir, lc_path, soil_path, pre_data, base_dir) for tif_file in tif_files]

    # 使用进程池并行处理
    print(f"开始使用 {num_workers} 个进程并行处理...")
    with ProcessPoolExecutor(max_workers=num_workers) as executor:
        # 使用list()确保所有任务完成
        list(executor.map(process_single_file, args_list))
    
    print("所有文件处理完成！")

def check_wtd_file(args):
    """
    检查单个wtd文件的状态
    
    参数:
    args: (basin_id, base_dir) 元组
    
    返回:
    (basin_id, needs_reprocessing) 元组
    """
    basin_id, base_dir = args
    wtd_file = f"{base_dir}/basin_{basin_id}-wtd.tif"
    
    needs_reprocessing = False
    
    # 检查文件是否存在
    if not os.path.exists(wtd_file):
        print(f"流域 {basin_id} 的wtd文件不存在")
        needs_reprocessing = True
    else:
        # 检查文件是否有效
        try:
            with rasterio.open(wtd_file) as src:
                data = src.read(1)
        except Exception as e:
            print(f"流域 {basin_id} 的wtd文件损坏: {str(e)}")
            needs_reprocessing = True
    
    return basin_id, needs_reprocessing

def check_and_reprocess_wtd_files(input_dir, base_dir, lc_path, soil_path, pre_path, output_dir, num_workers=32):
    """
    并行检查所有流域的wtd文件是否完整有效，重新处理缺失或无效的文件
    """
    print("开始并行检查wtd文件完整性...")
    
    # 获取所有DEM文件的流域ID
    dem_files = [f for f in os.listdir(input_dir) if f.endswith('.tif')]
    basin_ids = [f.split('_')[1] for f in dem_files]
    
    # 准备参数列表
    args_list = [(basin_id, base_dir) for basin_id in basin_ids]
    
    # 使用进程池并行检查文件
    reprocess_basins = []
    with ProcessPoolExecutor(max_workers=num_workers) as executor:
        results = list(executor.map(check_wtd_file, args_list))
        
        # 收集需要重新处理的流域
        for basin_id, needs_reprocessing in results:
            if needs_reprocessing:
                reprocess_basins.append(basin_id)
    
    # 如果有需要重新处理的流域
    if reprocess_basins:
        print(f"发现 {len(reprocess_basins)} 个流域需要重新处理")
        
        # 准备重新处理所需的参数
        temp_output_dir = output_dir
        if not os.path.exists(temp_output_dir):
            os.makedirs(temp_output_dir)
        
        # 读取降水数据
        pre_data = pd.read_csv(pre_path)
        
        # 重新处理每个问题流域
        for basin_id in reprocess_basins:
            print(f"重新处理流域 {basin_id}")
            tif_file = f"basin_{basin_id}_DEM.tif"
            args = (tif_file, input_dir, temp_output_dir, lc_path, soil_path, pre_data, base_dir)
            process_single_file(args)
        
        print("重新处理完成")
    else:
        print("所有流域的wtd文件都完整有效")
    
    # 删除base_dir下所有不是-wtd.tif结尾的文件
    print("开始清理非wtd文件...")
    for file in os.listdir(base_dir):
        file_path = os.path.join(base_dir, file)
        if os.path.isfile(file_path) and not file.endswith('-wtd.tif'):
            try:
                os.remove(file_path)
                print(f"已删除: {file_path}")
            except Exception as e:
                print(f"删除文件 {file_path} 时出错: {str(e)}")
    print("文件清理完成")



# 主程序
ssps = ['ssp126', 'ssp245', 'ssp370', 'ssp585']
models = {'ACCESS-CM2', 'ACCESS-ESM1-5', 'BCC-CSM2-MR', 'CanESM5', 'CMCC-ESM2', 'CNRM-CM6-1', 'CNRM-ESM2-1', 'EC-Earth3', 'EC-Earth3-Veg-LR', 'FGOALS-g3', 'GFDL-ESM4', 'GISS-E2-1-G', 'INM-CM4-8', 'INM-CM5-0', 'IPSL-CM6A-LR', 'MIROC6', 'MIROC-ES2L', 'MPI-ESM1-2-HR', 'MPI-ESM1-2-LR', 'MRI-ESM2-0','NorESM2-LM','NorESM2-MM','TaiESM1'}
input_dir = r"/mnt/sdb2/yuanshuai/wanpan/yuan/paper3/FSM/basins_DEM_lev05/Fathom"
soil_path = r"/mnt/sdb2/yuanshuai/wanpan/yuan/Flood/FSM/soil_group/HYSOGs250m_reclass.tif"

for model in models:
    print(f"正在处理模式: {model}")
    
    for ssp in ssps:
        print(f"正在处理情景: {ssp}")
        
        # 循环处理每个5年间隔的年份
        for end_year in range(2025, 2105, 5):
            print(f"正在处理年份: {end_year}")
            
            # 设置输出目录和土地利用数据路径
            output_dir = r"/mnt/sdb3/yuanshuai/temp_Q/{model}/{ssp}/{end_year}".format(model=model, ssp=ssp, end_year=end_year)
            lc_path = r"/mnt/sdb2/yuanshuai/wanpan/yuan/Flood/FSM/LC/ori_data/SSP/{ssp}/simulation{end_year}.tif".format(ssp=ssp, end_year=end_year)
            pre_path = r"/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03/pre_data/ssp/basins_EPEs_corrected_5years2/{ssp}/{model}/EPEs_{end_year}.csv".format(ssp=ssp, model=model, end_year=end_year)
            base_dir = r"/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03/FSM/res_lev05/SSP/{model}/{ssp}/{end_year}".format(model=model, ssp=ssp, end_year=end_year)
            
            # 处理Q值计算和FSM运行
            process_q_values(input_dir, output_dir, lc_path, soil_path, pre_path, base_dir, num_workers=16)
            
            check_and_reprocess_wtd_files(input_dir, base_dir, lc_path, soil_path, pre_path, output_dir, num_workers=16)